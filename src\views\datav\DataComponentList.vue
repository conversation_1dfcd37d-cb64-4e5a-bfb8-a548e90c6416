<template>
  <div class="page">
    <el-form
      :inline="true"
      v-if="searchVisible"
      class="query-form m-b-10"
      ref="searchForm"
      :model="searchForm"
      @keyup.enter="refreshList()"
      @submit.prevent
    >
      <!-- 搜索框-->
      <el-form-item prop="name" label="组件名称：">
        <el-input v-model="searchForm.name" placeholder="请输入组件名称" clearable></el-input>
		   </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="refreshList()" icon="search">查询</el-button>
        <el-button type="default" @click="resetSearch()" icon="refresh-right">重置</el-button>
      </el-form-item>
    </el-form>

		<div class="jp-table">
		  <vxe-toolbar ref="dataComponentToolbar" :refresh="{query: refreshList}" import export print custom>
		    <template #buttons>
				<el-button v-if="hasPermission('datav:dataComponent:add')" type="primary"  icon="plus" @click="add()">新建</el-button>
				<el-button v-if="hasPermission('datav:dataComponent:edit')" type="warning" icon="edit-filled" @click="edit()" v-show="$refs.dataComponentTable && $refs.dataComponentTable.getCheckboxRecords().length === 1" plain>修改</el-button>
				<el-button v-if="hasPermission('datav:dataComponent:del')" type="danger"   icon="del-filled" @click="del()" v-show="$refs.dataComponentTable && $refs.dataComponentTable.getCheckboxRecords().length > 0" plain>删除</el-button>
		    </template>
		    <template #tools>
		<vxe-button
		  type="text"
		  :title="
		    searchVisible ? '收起检索' : '展开检索'
		  "
		  icon="vxe-icon-search"
		  class="tool-btn"
		  @click="searchVisible = !searchVisible"
		></vxe-button>
		<vxe-button
		  type="text"
		  title="下载导入模板"
		  v-if="hasPermission('datav:dataComponent:import')"
		  icon="iconfont icon-xiazaimoban1"
		  class="tool-btn m-l-0"
		  @click="downloadTpl()"
		>
		</vxe-button>
    </template>
		  </vxe-toolbar>
		  <div class="jp-table-body">
		  <vxe-table
				border="inner"
				auto-resize
				resizable
				height="auto"
				:loading="loading"
				size="small"
				ref="dataComponentTable"
				show-header-overflow
				show-overflow
				highlight-hover-row
				:menu-config="{}"
				:print-config="{}"
  :import-config="{
    importMethod: importMethod,
    types: ['csv', 'xls', 'xlsx'],
    remote: true,
  }"
  :export-config="{
    remote: true,
    filename: `大屏组件数据${moment(new Date()).format(
		'YYYY-MM-DD'
    )}`,
    sheetName: '大屏组件数据',
    exportMethod: exportMethod,
    types: ['xlsx'],
    modes: ['current', 'selected', 'all'],
  }"

				@sort-change="sortChangeHandle"
				:sort-config="{remote:true}"
				:data="dataList"
				:checkbox-config="{}">
				<vxe-column type="seq" width="40"></vxe-column>
				<vxe-column type="checkbox"  width="40px"></vxe-column>
    <vxe-column
		  field="name"
		  sortable
		  title="组件名称">
				<template  #default="{ row }">
				  <el-link  type="primary" :underline="false" v-if="hasPermission('datav:dataComponent:edit')" @click="edit(row.id)">{{row.name}}</el-link>
				  <el-link  type="primary" :underline="false" v-else-if="hasPermission('datav:dataComponent:view')"  @click="view(row.id)">{{row.name}}</el-link>
				  <span v-else>{{row.name}}</span>
				</template>
		</vxe-column>
		<vxe-column
		  fixed="right"
		  align="center"
		  width="200"
		  title="操作">
		  <template   #default="{ row }">
		    <el-button v-if="hasPermission('datav:dataComponent:view')" type="primary" text icon="view-filled" @click="handleView(row.id)">预览</el-button>
		    <el-button v-if="hasPermission('datav:dataComponent:edit')" type="primary" text icon="edit-filled" @click="edit(row.id)">修改</el-button>
		    <el-button v-if="hasPermission('datav:dataComponent:del')"  type="danger"  text icon="del-filled" @click="del(row.id)">删除</el-button>
		  </template>
		</vxe-column>
    </vxe-table>
    <vxe-pager
		background
		size="small"
		:current-page="tablePage.currentPage"
		:page-size="tablePage.pageSize"
		:total="tablePage.total"
		:page-sizes="[10, 20, 100, 1000, {label: '全量数据', value: 1000000}]"
		:layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
		@page-change="currentChangeHandle">
    </vxe-pager>
    </div>
    </div>
		  <!-- 弹窗, 新增 / 修改 -->
    <DataComponentForm  ref="dataComponentForm" @refreshDataList="refreshList"></DataComponentForm>
	<el-dialog title="预览组件" v-model="box" width="60%" height="500px">
		<iframe :src="`/datav.html#/componentsPreview?id=${boxId}`" style="height:500px; width:100%">
		</iframe>
	</el-dialog>

  </div>
</template>

<script>
  import DataComponentForm from './DataComponentForm'
  import dataComponentService from '@/api/datav/dataComponentService'
  export default {
    data () {
		return {
		  searchVisible: true,
		  box: false,
		  boxId: '',
		  vueOption: {},
		  searchForm: {
		    name: ''
		  },
		  dataList: [],
		  tablePage: {
		    total: 0,
		    currentPage: 1,
		    pageSize: 10,
		    orders: [{ column: "create_time", asc: false }],
		  },
		  loading: false
		}
    },
    components: {
		DataComponentForm
    },
    created () {
    },    
    mounted () {
		this.$nextTick(() => {
		  // 将表格和工具栏进行关联
		  const $table = this.$refs.dataComponentTable
		  const $toolbar = this.$refs.dataComponentToolbar
		  $table.connect($toolbar)
		})
    }, 
    activated () {
		this.refreshList()
    },
    methods: {
		// 获取数据列表
		refreshList () {
		  this.loading = true
		  dataComponentService.list({
		    'current': this.tablePage.currentPage,
		    'size': this.tablePage.pageSize,
		    'orders': this.tablePage.orders,
		    ...this.searchForm
		  }).then((data) => {
		    this.dataList = data.records
		    this.tablePage.total = data.total
		    this.loading = false
		  })
		},
		// 当前页
		currentChangeHandle ({ currentPage, pageSize }) {
		  this.tablePage.currentPage = currentPage
		  this.tablePage.pageSize = pageSize
		  this.refreshList()
		},
		// 排序
    	sortChangeHandle(obj) {
		  this.tablePage.orders = [];
		  if (obj.order != null) {
		    this.tablePage.orders = [{ column: obj.column.sortBy || this.$utils.toLine(obj.property), asc: obj.order === "asc" }];
		  } else {
		    this.tablePage.orders = [{ column: "create_time", asc: false }];
		  }
		  this.refreshList();
		},
		// 新增
		add () {
		  this.$refs.dataComponentForm.init('add', '')
		},
		// 修改
		edit (id) {
		  id = id || this.$refs.dataComponentTable.getCheckboxRecords().map(item => {
		    return item.id
		  })[0]
		  this.$refs.dataComponentForm.init('edit', id)
		},
		// 查看
		view (id) {
		  this.$refs.dataComponentForm.init('view', id)
		},
		// 预览
		handleView(id) {
			this.boxId = id;
			this.box = true;
		},
		// 删除
		del (id) {
		  let ids = id || this.$refs.dataComponentTable.getCheckboxRecords().map(item => {
		    return item.id
		  }).join(',')
		  this.$confirm(`确定删除所选项吗?`, '提示', {
		    confirmButtonText: '确定',
		    cancelButtonText: '取消',
		    type: 'warning'
		  }).then(() => {
		    this.loading = true
		    dataComponentService.delete(ids).then((data) => {
				this.$message.success(data)
				this.refreshList()
				this.loading = false
		    })
		  })
		},
		// 下载模板
		downloadTpl() {
		this.loading = true
		dataComponentService
		  .exportTemplate()
		  .then((data) => {
		    // 将二进制流文件写入excel表，以下为重要步骤
		    this.$utils.downloadExcel(data, "大屏组件导入模板");
		    this.loading = false
		  })
		  .catch(function (err) {
				this.loading = false
		    if (err.response) {
				console.log(err.response);
		    }
		  });
    },
	resetSearch () {
		this.$refs.searchForm.resetFields()
		this.refreshList()
	}
    }
  }
</script>


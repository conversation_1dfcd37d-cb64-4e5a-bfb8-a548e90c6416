import { createI18n } from "vue-i18n";
import tool from "@/utils/tool";
import zhCN from "vxe-table/lib/locale/lang/zh-CN";
import enUS from "vxe-table/lib/locale/lang/en-US";
import jaJP from "vxe-table/lib/locale/lang/ja-JP";

const messages = {
    zh: {
        ...zhCN,
    },
    en: {
        ...enUS,
    },
    ja: {
        ...jaJP,
    },
};

const i18n = createI18n({
    locale:
        tool.data.get("APP_LANG") === "zh"
            ? "zh"
            : tool.data.get("APP_LANG") || "zh",
    messages,
});

export default i18n;

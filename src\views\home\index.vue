<template>
	<div class="container">
		<div class="left-side">
			<div class="panel">
				<Banner />
				<!-- <DataPanel /> -->
				<ContentChart />
			</div>
			<el-row :gutter="16" style="margin-top: 16px">
				<el-col :span="12">
					<CateforiesPercent />
				</el-col>
				<el-col :span="12">
					<Carousel />
				</el-col>
			</el-row>
		</div>
		<div class="right-side">
			<el-row :gutter="16">
				<el-col :span="24">
					<div class="panel moduler-wrap">
						<QuickOperation />
					</div>
				</el-col>
				<!-- <el-col :span="24">
          <div class="panel moduler-wrap">
            <RecentlyVisited />
          </div>
        </el-col> -->
				<el-col class="panel" :span="24">
					<Announcement />
				</el-col>
				<el-col class="panel" :span="12">
					<Docs />
				</el-col>
				<el-col class="panel" :span="12">
					<Wx />
				</el-col>
			</el-row>
		</div>
	</div>
</template>

<script>
import Banner from "./components/banner.vue";
import DataPanel from "./components/data-panel.vue";
import ContentChart from "./components/content-chart.vue";
import PopularContent from "./components/popular-content.vue";
import CateforiesPercent from "./components/categories-percent.vue";
import RecentlyVisited from "./components/recently-visited.vue";
import QuickOperation from "./components/quick-operation.vue";
// import TodoList from './components/todoList.vue'
import Announcement from "./components/announcement.vue";
import Carousel from "./components/carousel.vue";
import Docs from "./components/docs.vue";
import Wx from "./components/wx.vue";
import Footer from "@/components/footer/index.vue";
export default {
	name: "Dashboard", // If you want the include property of keep-alive to take effect, you must name the component
	components: {
		Banner,
		DataPanel,
		ContentChart,
		PopularContent,
		CateforiesPercent,
		RecentlyVisited,
		QuickOperation,
		Announcement,
		Carousel,
		Docs,
		Wx,
		Footer,
	},
};
</script>

<style lang="less" scoped>
.adminui-topbar + .container {
	padding: 12px 12px 8px 12px;
}
.container {
	padding: 8px 12px;
	display: flex;
}

.left-side {
	flex: 1;
	overflow: hidden;
	.panel {
		background-color: #fff;
	}
}

.right-side {
	width: 680px;
	margin-left: 16px;
}

.panel {
	border-radius: 4px;
	overflow: hidden;
	margin-bottom: 16px;
}
:deep(.panel-border) {
	margin-bottom: 0;
	border-bottom: 1px solid rgb(242, 243, 245);
}
.moduler-wrap {
	border-radius: 4px;
	background-color: #fff;
	:deep(.text) {
		font-size: 14px;
		text-align: center;
		color: #515a6e;
	}

	:deep(.wrapper) {
		margin-bottom: 8px;
		text-align: center;
		cursor: pointer;

		&:last-child {
			.text {
				margin-bottom: 0;
			}
		}
		&:hover {
			.text {
				color: var(--el-color-primary);
			}
		}
	}

	:deep(.icon) {
		display: inline-block;
		margin-bottom: 4px;
		line-height: 32px;
		font-size: 16px;
		text-align: center;
		background-color: rgb(247, 248, 250);
		border-radius: 4px;
	}
}
</style>

<style lang="less" scoped>
// responsive
.mobile {
	.container {
		display: block;
		margin-bottom: 10px;
	}
	.right-side {
		// display: none;
		width: 100%;
		margin-left: 0;
		margin-top: 16px;
	}
}
</style>

import { createApp } from "vue";
import axios from "./axios";
import router from "./router.js";
import ElementPlus from "element-plus";
import createIcon from "./icon";
import "element-plus/dist/index.css";
import zhCn from "element-plus/dist/locale/zh-cn.mjs";
import Avue from "@smallwei/avue";
import { website } from "./config.js";
import { loadScript } from "./utils/utils";
import "@smallwei/avue/lib/index.css";
import draggable from "./page/components/draggable.vue";
import error from "./error";
import App from "./App.vue";
import "./styles/common.scss";
import "./utils/es6";
const app = createApp(App);
window.axios = axios;
document.title = website.title;
createIcon(app);
app.component("avue-draggable", draggable);
app.config.globalProperties.$component = app.component;
app.config.globalProperties.$website = website;
window.$loadScript = loadScript;
app.use(error);
app.use(router);
app.use(ElementPlus, {
	locale: zhCn,
});
app.use(Avue, {
	axios,
});
app.mount("#app");

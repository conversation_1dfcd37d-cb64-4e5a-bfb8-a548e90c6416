<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试左右切换功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #476CFF;
            border-radius: 8px;
        }
        .test-title {
            color: #476CFF;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .test-steps {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .step {
            margin-bottom: 10px;
            padding: 8px;
            background: white;
            border-left: 4px solid #28a745;
            padding-left: 12px;
        }
        .expected {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .warning {
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            margin-top: 15px;
        }
        .code {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 告警图片左右切换功能测试指南</h1>
        
        <div class="test-section">
            <div class="test-title">📋 测试准备</div>
            <div class="test-steps">
                <div class="step">1. 确保项目已启动 (npm run dev)</div>
                <div class="step">2. 打开浏览器开发者工具的控制台 (F12)</div>
                <div class="step">3. 导航到包含 videoAlertPanel 组件的页面</div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 测试模式设置</div>
            <div class="test-steps">
                <div class="step">1. 在页面右上角找到 "测试模式" 开关</div>
                <div class="step">2. 将开关切换到 "测试模式" (绿色状态)</div>
                <div class="step">3. 观察控制台输出: "🧪 测试模式：使用模拟数据"</div>
            </div>
            <div class="expected">
                <strong>预期结果:</strong> 页面显示5条测试告警数据，每条都有不同的随机图片
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🖱️ 基础切换测试</div>
            <div class="test-steps">
                <div class="step">1. 点击任意一张告警图片打开详情弹窗</div>
                <div class="step">2. 观察弹窗左右两侧是否显示 "&lt;" 和 "&gt;" 按钮</div>
                <div class="step">3. 查看弹窗底部的调试信息 (黄色背景)</div>
                <div class="step">4. 点击右侧 "&gt;" 按钮切换到下一条</div>
                <div class="step">5. 点击左侧 "&lt;" 按钮切换到上一条</div>
            </div>
            <div class="expected">
                <strong>预期结果:</strong> 
                <ul>
                    <li>图片和详情信息会随着点击而改变</li>
                    <li>调试信息显示当前位置 (如: "2 / 5")</li>
                    <li>控制台输出切换日志</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🚫 边界条件测试</div>
            <div class="test-steps">
                <div class="step">1. 切换到第一条数据 (1/5)</div>
                <div class="step">2. 观察左侧 "&lt;" 按钮是否变为灰色禁用状态</div>
                <div class="step">3. 尝试点击禁用的左按钮</div>
                <div class="step">4. 切换到最后一条数据 (5/5)</div>
                <div class="step">5. 观察右侧 "&gt;" 按钮是否变为灰色禁用状态</div>
                <div class="step">6. 尝试点击禁用的右按钮</div>
            </div>
            <div class="expected">
                <strong>预期结果:</strong> 
                <ul>
                    <li>第一条数据时左按钮禁用，无法继续向前</li>
                    <li>最后一条数据时右按钮禁用，无法继续向后</li>
                    <li>控制台显示 "❌ 已经是第一条/最后一条数据，无法切换"</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔍 控制台调试信息</div>
            <div class="code">
                关键日志信息：<br>
                🧪 测试模式：使用模拟数据<br>
                📊 所有告警数据: [5条数据]<br>
                🔍 查找当前数据在全部数据中的位置<br>
                📍 设置当前索引为: X (总共5条数据)<br>
                ⬅️ 尝试切换到上一条，当前索引: X<br>
                ➡️ 尝试切换到下一条，当前索引: X<br>
                🔄 更新预览数据，索引: X
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔄 正常模式测试</div>
            <div class="test-steps">
                <div class="step">1. 将测试模式开关切换回 "正常模式" (红色状态)</div>
                <div class="step">2. 观察是否能正常加载真实的告警数据</div>
                <div class="step">3. 测试切换功能是否在真实数据中也能正常工作</div>
            </div>
            <div class="warning">
                <strong>注意:</strong> 正常模式需要真实的后端接口支持，如果没有数据或图片无法显示，这是正常的。
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">✅ 测试检查清单</div>
            <div class="test-steps">
                <div class="step">□ 测试模式开关正常工作</div>
                <div class="step">□ 显示5条测试数据</div>
                <div class="step">□ 左右按钮显示 "&lt;" 和 "&gt;" 符号</div>
                <div class="step">□ 点击按钮能切换图片和信息</div>
                <div class="step">□ 第一条数据左按钮禁用</div>
                <div class="step">□ 最后一条数据右按钮禁用</div>
                <div class="step">□ 调试信息正确显示当前位置</div>
                <div class="step">□ 控制台输出详细的切换日志</div>
            </div>
        </div>

        <div class="warning">
            <strong>🔧 如果遇到问题:</strong>
            <ul>
                <li>检查浏览器控制台是否有错误信息</li>
                <li>确认 isTestMode 变量是否正确设置为 true</li>
                <li>验证 allAlarmData 数组是否包含5条测试数据</li>
                <li>检查 currentPreviewIndex 是否正确更新</li>
            </ul>
        </div>
    </div>
</body>
</html>

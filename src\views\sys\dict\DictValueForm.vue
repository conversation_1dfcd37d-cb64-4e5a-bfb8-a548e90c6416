<template>
	<v-dialog
		:title="!inputForm.id ? '新增' : '修改'"
		:close-on-click-modal="false"
		v-model="visible"
	>
		<el-form
			:model="inputForm"
			:rules="dataRule"
			v-loading="loading"
			ref="inputForm"
			@keyup.enter="doSubmit()"
			label-width="80px"
			@submit.prevent
		>
			<el-form-item label="标签" prop="label">
				<el-input
					v-model="inputForm.label"
					placeholder="标签"
				></el-input>
			</el-form-item>
			<el-form-item label="键值" prop="value">
				<el-input
					v-model="inputForm.value"
					placeholder="键值"
				></el-input>
			</el-form-item>
			<el-form-item label="排序号" prop="sort">
				<el-input-number
					style="width: 100%"
					:step="1"
					v-model="inputForm.sort"
					placeholder="排序号"
				></el-input-number>
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false" icon="circle-close"
					>关闭</el-button
				>
				<el-button
					v-if="method != 'view'"
					type="primary"
					@click="doSubmit()"
					icon="circle-check"
					v-noMoreClick
					>确定</el-button
				>
			</span>
		</template>
	</v-dialog>
</template>

<script>
import dictService from "@/api/sys/dictService";
export default {
	data() {
		return {
			visible: false,
			loading: false,
			inputForm: {
				id: "",
				dictTypeId: "",
				label: "",
				value: "",
				sort: 1,
			},
			dataRule: {
				label: [
					{
						required: true,
						message: "标签不能为空",
						trigger: "blur",
					},
				],
				value: [
					{
						required: true,
						message: "键值不能为空",
						trigger: "blur",
					},
				],
			},
		};
	},
	methods: {
		init(method, obj) {
			this.inputForm.id = obj.dictValueId;
			this.inputForm.dictTypeId = obj.dictTypeId;
			this.visible = true;
			this.$nextTick(() => {
				this.$refs["inputForm"].resetFields();
				if (method === "edit" || method === "view") {
					// 修改或者查看
					dictService
						.queryDictValue(this.inputForm.id)
						.then((data) => {
							this.inputForm = this.recover(this.inputForm, data);
							this.inputForm.id = obj.dictValueId;
						});
				}
			});
		},
		// 表单提交
		doSubmit() {
			if (!this.groupId) {
				this.groupWrong = "请选择分组";
			}
			this.$refs["inputForm"].validate((valid) => {
				if (valid) {
					this.loading = true;
					dictService
						.saveDictValue(this.inputForm)
						.then((data) => {
							this.loading = false;
							this.$message.success(data);
							this.visible = false;
							this.$emit("refreshDataList");
							this.$dictUtils.refreshDictList();
						})
						.catch(() => {
							this.loading = false;
						});
				}
			});
		},
	},
};
</script>

<template>
	<v-dialog
		title="退回任务"
		:close-on-click-modal="false"
		v-model="visible"
		:appendToBody="true"
	>
		<el-select
			v-model="backTaskDefKey"
			style="width: 100%"
			placeholder="请选择驳回节点"
		>
			<el-option
				v-for="item in backNodes"
				:key="item.taskDefKey"
				:label="item.taskName"
				:value="item.taskDefKey"
			>
			</el-option>
		</el-select>

		<template #footer>
			<span class="dialog-footer">
				<el-button icon="close" @click="visible = false"
					>取消</el-button
				>
				<el-button icon="check" type="primary" @click="doConfirm"
					>确定</el-button
				>
			</span>
		</template>
	</v-dialog>
</template>

<script>
import taskService from "@/api/flowable/taskService";
export default {
	name: "TaskBackNodes",
	computed: {
		dialogTaskBackNodesInChild: {
			get() {
				return this.visible;
			},
			set(val) {
				this.$emit("update:visible", val);
			},
		},
	},
	data() {
		return {
			visible: false,
			backNodes: [],
			backTaskDefKey: "",
		};
	},
	methods: {
		init(taskId) {
			this.visible = true;
			taskService.backNodes(taskId).then((data) => {
				this.backNodes = data;
			});
		},
		doConfirm() {
			this.visible = false;
			this.$emit("getBackTaskDefKey", this.backTaskDefKey);
		},
	},
};
</script>

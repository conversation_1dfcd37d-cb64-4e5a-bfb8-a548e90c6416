<template>
	<div class="page">
		<el-form
			:inline="true"
			class="query-form m-b-10"
			v-if="searchVisible"
			ref="searchForm"
			:model="searchForm"
			@keyup.enter="refreshList()"
			@submit.prevent
		>
			<!-- 搜索框-->
			<!-- 搜索框-->
			<el-form-item prop="code" label="编码：">
				<el-input
					v-model="searchForm.code"
					placeholder="请输入编码"
					clearable
				></el-input>
			</el-form-item>
			<el-form-item prop="code" label="中文：">
				<el-input
					v-model="searchForm.zh"
					placeholder="请输入中文"
					clearable
				></el-input>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="refreshList()" icon="search"
					>查询</el-button
				>
				<el-button
					type="default"
					@click="resetSearch()"
					icon="refresh-right"
					>重置</el-button
				>
			</el-form-item>
		</el-form>
		<div class="jp-table">
			<vxe-toolbar
				ref="languageToolbar"
				:refresh="{ query: refreshList }"
				export
				print
				custom
			>
				<template #buttons>
					<el-button
						v-if="hasPermission('sys:language:add')"
						type="primary"
						icon="plus"
						@click="add()"
						>新建</el-button
					>
					<el-button
						v-if="hasPermission('sys:language:edit')"
						type="warning"
						icon="edit-filled"
						@click="edit()"
						v-show="
							$refs.languageTable &&
							$refs.languageTable.getCheckboxRecords().length ===
								1
						"
						>修改</el-button
					>
					<el-button
						v-if="hasPermission('sys:language:del')"
						type="danger"
						icon="del-filled"
						@click="del()"
						v-show="
							$refs.languageTable &&
							$refs.languageTable.getCheckboxRecords().length > 0
						"
						>删除</el-button
					>
				</template>
				<template #tools>
					<vxe-button
						type="text"
						:title="searchVisible ? '收起检索' : '展开检索'"
						icon="vxe-icon-search"
						class="tool-btn"
						@click="searchVisible = !searchVisible"
					></vxe-button>
				</template>
			</vxe-toolbar>
			<div class="jp-table-body">
				<vxe-table
					border="inner"
					auto-resize
					resizable
					height="auto"
					:loading="loading"
					size="small"
					ref="languageTable"
					show-header-overflow
					show-overflow
					highlight-hover-row
					:menu-config="{}"
					:print-config="{}"
					:import-config="{}"
					:export-config="{}"
					@sort-change="sortChangeHandle"
					:sort-config="{ remote: true }"
					:data="dataList"
					:checkbox-config="{}"
				>
					<vxe-column type="seq" width="40"></vxe-column>
					<vxe-column type="checkbox" width="40px"></vxe-column>
					<vxe-column title="编码" field="code" sortable>
						<template #default="{ row }">
							<el-link
								type="primary"
								:underline="false"
								v-if="hasPermission('sys:language:edit')"
								@click="edit(row.id)"
								>{{ row.code }}</el-link
							>
							<el-link
								type="primary"
								:underline="false"
								v-else-if="hasPermission('sys:language:view')"
								@click="view(row.id)"
								>{{ row.code }}</el-link
							>
							<span v-else>{{ row.code }}</span>
						</template>
					</vxe-column>
					<vxe-column title="中文" field="zh" sortable> </vxe-column>
					<vxe-column title="英文" field="en" sortable> </vxe-column>
					<vxe-column title="日语" field="ja" sortable></vxe-column>
					<vxe-column
						title="操作"
						width="200px"
						fixed="right"
						align="center"
					>
						<template #default="{ row }">
							<el-button
								v-if="hasPermission('sys:language:view')"
								text
								type="primary"
								icon="view-filled"
								@click="view(row.id)"
								>查看</el-button
							>
							<el-button
								v-if="hasPermission('sys:language:edit')"
								text
								type="primary"
								icon="edit-filled"
								@click="edit(row.id)"
								>修改</el-button
							>
							<el-button
								v-if="hasPermission('sys:language:del')"
								text
								type="danger"
								icon="del-filled"
								@click="del(row.id)"
								>删除</el-button
							>
						</template>
					</vxe-column>
				</vxe-table>
				<vxe-pager
					background
					size="small"
					:current-page="tablePage.currentPage"
					:page-size="tablePage.pageSize"
					:total="tablePage.total"
					:page-sizes="[
						10,
						20,
						100,
						1000,
						{ label: '全量数据', value: 1000000 },
					]"
					:layouts="[
						'PrevPage',
						'JumpNumber',
						'NextPage',
						'FullJump',
						'Sizes',
						'Total',
					]"
					@page-change="currentChangeHandle"
				>
				</vxe-pager>
			</div>
		</div>
		<!-- 弹窗, 新增 / 修改 -->
		<language-form
			ref="languageForm"
			@refreshDataList="refreshList"
		></language-form>
	</div>
</template>

<script>
import LanguageForm from "./LanguageForm";
import languageService from "@/api/sys/languageService";
export default {
	data() {
		return {
			searchVisible: true,
			searchForm: {
				name: "",
				zh: "",
				code: "",
			},
			dataList: [],
			tablePage: {
				total: 0,
				currentPage: 1,
				pageSize: 10,
				orders: [{ column: "create_time", asc: false }],
			},
			loading: false,
		};
	},
	components: {
		LanguageForm,
	},
	mounted() {
		this.$nextTick(() => {
			// 将表格和工具栏进行关联
			const $table = this.$refs.languageTable;
			const $toolbar = this.$refs.languageToolbar;
			$table.connect($toolbar);
		});
		this.refreshList();
	},

	methods: {
		// 获取数据列表
		refreshList() {
			this.loading = true;
			languageService
				.list({
					current: this.tablePage.currentPage,
					size: this.tablePage.pageSize,
					orders: this.tablePage.orders,
					...this.searchForm,
				})
				.then((data) => {
					this.dataList = data.records;
					this.tablePage.total = data.total;
					this.loading = false;
				});
		},
		// 当前页
		currentChangeHandle({ currentPage, pageSize }) {
			this.tablePage.currentPage = currentPage;
			this.tablePage.pageSize = pageSize;
			this.refreshList();
		},
		// 排序
		sortChangeHandle(column) {
			this.tablePage.orders = [];
			if (column.order != null) {
				this.tablePage.orders.push({
					column: this.$utils.toLine(column.property),
					asc: column.order === "asc",
				});
			} else {
				this.tablePage.orders = [{ column: "create_time", asc: false }];
			}
			this.refreshList();
		},
		// 新增
		add() {
			this.$refs.languageForm.init("add", "");
		},
		// 修改
		edit(id) {
			id =
				id ||
				this.$refs.languageTable.getCheckboxRecords().map((item) => {
					return item.id;
				})[0];
			this.$refs.languageForm.init("edit", id);
		},
		// 查看
		view(id) {
			this.$refs.languageForm.init("view", id);
		},
		// 删除
		del(id) {
			let ids =
				id ||
				this.$refs.languageTable
					.getCheckboxRecords()
					.map((item) => {
						return item.id;
					})
					.join(",");
			this.$confirm(`确定删除所选项吗?`, "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
			}).then(() => {
				this.loading = true;
				languageService.delete(ids).then((data) => {
					this.$message.success(data);
					this.refreshList();
					this.loading = false;
				});
			});
		},
		resetSearch() {
			this.$refs.searchForm.resetFields();
			this.refreshList();
		},
	},
};
</script>

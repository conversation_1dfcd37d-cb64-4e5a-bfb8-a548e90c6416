<template>
  <div class="page">
    <el-form
      :inline="true"
      v-if="searchVisible"
      class="query-form m-b-10"
      ref="searchForm"
      :model="searchForm"
      @keyup.enter="refreshList()"
      @submit.prevent
    >
      <!-- 搜索框-->
      <el-form-item prop="title" label="标题：">
        <el-input v-model="searchForm.title" placeholder="请输入标题" clearable></el-input>
		   </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="refreshList()" icon="search">查询</el-button>
        <el-button type="default" @click="resetSearch()" icon="refresh-right">重置</el-button>
      </el-form-item>
    </el-form>

		<div class="jp-table">
		  <vxe-toolbar ref="testPicToolbar" :refresh="{query: refreshList}" import export print custom>
		    <template #buttons>
				<el-button v-if="hasPermission('test:pic:testPic:add')" type="primary"  icon="plus" @click="add()">新建</el-button>
				<el-button v-if="hasPermission('test:pic:testPic:edit')" type="warning" icon="edit-filled" @click="edit()" v-show="$refs.testPicTable && $refs.testPicTable.getCheckboxRecords().length === 1" plain>修改</el-button>
				<el-button v-if="hasPermission('test:pic:testPic:del')" type="danger"   icon="del-filled" @click="del()" v-show="$refs.testPicTable && $refs.testPicTable.getCheckboxRecords().length > 0" plain>删除</el-button>
		    </template>
		    <template #tools>
		<vxe-button
		  type="text"
		  :title="
		    searchVisible ? '收起检索' : '展开检索'
		  "
		  icon="vxe-icon-search"
		  class="tool-btn"
		  @click="searchVisible = !searchVisible"
		></vxe-button>
		<vxe-button
		  type="text"
		  title="下载导入模板"
		  v-if="hasPermission('test:pic:testPic:import')"
		  icon="iconfont icon-xiazaimoban1"
		  class="tool-btn m-l-0"
		  @click="downloadTpl()"
		>
		</vxe-button>
    </template>
		  </vxe-toolbar>
		  <div class="jp-table-body">
		  <vxe-table
				border="inner"
				auto-resize
				resizable
				height="auto"
				:loading="loading"
				size="small"
				ref="testPicTable"
				show-header-overflow
				show-overflow
				highlight-hover-row
				:menu-config="{}"
				:print-config="{}"
  :import-config="{
    importMethod: importMethod,
    types: ['csv', 'xls', 'xlsx'],
    remote: true,
  }"
  :export-config="{
    remote: true,
    filename: `图片数据${moment(new Date()).format(
		'YYYY-MM-DD'
    )}`,
    sheetName: '图片数据',
    exportMethod: exportMethod,
    types: ['xlsx'],
    modes: ['current', 'selected', 'all'],
  }"

				@sort-change="sortChangeHandle"
				:sort-config="{remote:true}"
				:data="dataList"
				:checkbox-config="{}">
				<vxe-column type="seq" width="40"></vxe-column>
				<vxe-column type="checkbox"  width="40px"></vxe-column>
    <vxe-column
		  field="title"
		  sortable
		  title="标题">
				<template  #default="{ row }">
				  <el-link  type="primary" :underline="false" v-if="hasPermission('test:pic:testPic:edit')" @click="edit(row.id)">{{row.title}}</el-link>
				  <el-link  type="primary" :underline="false" v-else-if="hasPermission('test:pic:testPic:view')"  @click="view(row.id)">{{row.title}}</el-link>
				  <span v-else>{{row.title}}</span>
				</template>
		</vxe-column>
    <vxe-column
		  field="pic"
		  sortable
		  title="图片路径">
		  <template  #default="{ row }">
		    <template v-if="row.pic">
				  <el-image
				    style="height: 50px;width:50px;margin-right:10px;"
				    :src="src" v-for="(src, index) in row.pic.split(',')" :key="index"
				    :preview-src-list="row.pic.split(',')">
				  </el-image>
		     </template>
		  </template>
		</vxe-column>
    <vxe-column
		  field="file"
		  sortable
		  title="文件">
		  <template #default="{ row }">
				<template v-if="row.file">
				    <el-link type="primary" class="m-r-10" :href="item" target="_blank" :key="index" v-for="(item, index) in row.file.split(',')"> {{decodeURIComponent(item.substring(item.lastIndexOf("&name=")+6))}}</el-link>
				</template>
		  </template>
		</vxe-column>
    <vxe-column
		  field="remarks"
		  sortable
		  title="备注信息">
		</vxe-column>
		<vxe-column
		  fixed="right"
		  align="center"
		  width="200"
		  title="操作">
		  <template   #default="{ row }">
		    <el-button v-if="hasPermission('test:pic:testPic:view')" type="primary" text icon="view-filled" @click="view(row.id)">查看</el-button>
		    <el-button v-if="hasPermission('test:pic:testPic:edit')" type="primary" text icon="edit-filled" @click="edit(row.id)">修改</el-button>
		    <el-button v-if="hasPermission('test:pic:testPic:del')"  type="danger"  text icon="del-filled" @click="del(row.id)">删除</el-button>
		  </template>
		</vxe-column>
    </vxe-table>
    <vxe-pager
		background
		size="small"
		:current-page="tablePage.currentPage"
		:page-size="tablePage.pageSize"
		:total="tablePage.total"
		:page-sizes="[10, 20, 100, 1000, {label: '全量数据', value: 1000000}]"
		:layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
		@page-change="currentChangeHandle">
    </vxe-pager>
    </div>
    </div>
		  <!-- 弹窗, 新增 / 修改 -->
    <TestPicForm  ref="testPicForm" @refreshDataList="refreshList"></TestPicForm>
  </div>
</template>

<script>
  import TestPicForm from './TestPicForm'
  import testPicService from '@/api/test/pic/testPicService'
  export default {
    data () {
		return {
		  searchVisible: true,
		  searchForm: {
		    title: ''
		  },
		  dataList: [],
		  tablePage: {
		    total: 0,
		    currentPage: 1,
		    pageSize: 10,
		    orders: [{ column: "create_time", asc: false }],
		  },
		  loading: false
		}
    },
    components: {
		TestPicForm
    },
    created () {
    },    
    mounted () {
		this.$nextTick(() => {
		  // 将表格和工具栏进行关联
		  const $table = this.$refs.testPicTable
		  const $toolbar = this.$refs.testPicToolbar
		  $table.connect($toolbar)
		})
    }, 
    activated () {
		this.refreshList()
    },
    methods: {
		// 获取数据列表
		refreshList () {
		  this.loading = true
		  testPicService.list({
		    'current': this.tablePage.currentPage,
		    'size': this.tablePage.pageSize,
		    'orders': this.tablePage.orders,
		    ...this.searchForm
		  }).then((data) => {
		    this.dataList = data.records
		    this.tablePage.total = data.total
		    this.loading = false
		  })
		},
		// 当前页
		currentChangeHandle ({ currentPage, pageSize }) {
		  this.tablePage.currentPage = currentPage
		  this.tablePage.pageSize = pageSize
		  this.refreshList()
		},
		// 排序
    sortChangeHandle(obj) {
		  this.tablePage.orders = [];
		  if (obj.order != null) {
		    this.tablePage.orders = [{ column: obj.column.sortBy || this.$utils.toLine(obj.property), asc: obj.order === "asc" }];
		  } else {
		    this.tablePage.orders = [{ column: "create_time", asc: false }];
		  }
		  this.refreshList();
		},
		// 新增
		add () {
		  this.$refs.testPicForm.init('add', '')
		},
		// 修改
		edit (id) {
		  id = id || this.$refs.testPicTable.getCheckboxRecords().map(item => {
		    return item.id
		  })[0]
		  this.$refs.testPicForm.init('edit', id)
		},
		// 查看
		view (id) {
		  this.$refs.testPicForm.init('view', id)
		},
		// 删除
		del (id) {
		  let ids = id || this.$refs.testPicTable.getCheckboxRecords().map(item => {
		    return item.id
		  }).join(',')
		  this.$confirm(`确定删除所选项吗?`, '提示', {
		    confirmButtonText: '确定',
		    cancelButtonText: '取消',
		    type: 'warning'
		  }).then(() => {
		    this.loading = true
		    testPicService.delete(ids).then((data) => {
				this.$message.success(data)
				this.refreshList()
				this.loading = false
		    })
		  })
		},
		// 下载模板
		downloadTpl() {
		this.loading = true
		testPicService
		  .exportTemplate()
		  .then((data) => {
		    // 将二进制流文件写入excel表，以下为重要步骤
		    this.$utils.downloadExcel(data, "图片导入模板");
		    this.loading = false
		  })
		  .catch(function (err) {
				this.loading = false
		    if (err.response) {
				console.log(err.response);
		    }
		  });
    },
    // 自定义服务端导入
    importMethod({ file }) {
		// 处理表单
		const formBody = new FormData();
		formBody.append("file", file);
		this.loading = true
		testPicService.importExcel(formBody).then((result) => {
		  this.$message.success({
		    dangerouslyUseHTMLString: true,
		    message: result,
		  });
		  this.refreshList();
		});
    },
    // 自定义服务端导出
    exportMethod({ options }) {
		// 传给服务端的参数
		const params = {
		  current: this.tablePage.currentPage,
		  size: this.tablePage.pageSize,
		  orders: this.tablePage.orders,
		  ...this.searchForm,
		  filename: options.filename,
		  sheetName: options.sheetName,
		  isHeader: options.isHeader,
		  original: options.original,
		  mode: options.mode,
		  selectIds:
		    options.mode === "selected"
				? options.data.map((item) => item.id)
				: [],
		  exportFields: options.columns.map(
		    (column) => column.property && column.property.split(".")[0]
		  ),
		};
		this.loading = true;
		return testPicService
		  .exportExcel(params)
		  .then((data) => {
		    // 将二进制流文件写入excel表，以下为重要步骤
		    this.$utils.downloadExcel(data, options.filename);
		    this.loading = false;
		  })
		  .catch(function (err) {
		    if (err.response) {
				console.log(err.response);
		    }
		  });
    },
		resetSearch () {
		  this.$refs.searchForm.resetFields()
		  this.refreshList()
		}
    }
  }
</script>


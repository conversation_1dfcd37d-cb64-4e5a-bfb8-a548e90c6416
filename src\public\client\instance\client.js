/*
 * @Author: hkh <EMAIL>
 * @Date: 2024-07-16 16:13:16
 * @LastEditors: hkh <EMAIL>
 * @LastEditTime: 2025-07-14 11:11:33
 * @FilePath: \chengde-computer-room\src\public\client\instance\client.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from "axios";
import { AppGlobalConfig } from "@/public/global/const";
axios.defaults.headers.post["Content-Type"] = "application/json";
const instance = axios.create({
    baseURL: AppGlobalConfig.ServerUrl,
});

// axios.defaults.withCredentials = true;

instance.interceptors.request.use(async (config) => {
    config.headers["api_token"] = localStorage.getItem("token") || AppGlobalConfig.token;
    return config;
});

instance.interceptors.response.use(
    (response) => {
        const { data, status } = response;
        if (status === 200) {
            return data;
        } else {
            return Promise.reject({
                msg: data.msg,
            });
        }
    },
    () => {
        return Promise.reject("服务器繁忙！");
    },
);

export default instance;

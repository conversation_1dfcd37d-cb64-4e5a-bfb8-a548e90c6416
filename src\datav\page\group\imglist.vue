<template>
	<el-dialog
		title="图库"
		width="80%"
		:close-on-click-modal="false"
		v-model="imgVisible"
	>
		<div style="margin: 0 auto">
			<el-upload
				class="upload-demo"
				:on-success="onSuccess"
				:show-file-list="false"
				:http-request="request"
				multiple
				list-type="picture"
			>
				<el-button size="small" icon="el-icon-upload" type="primary"
					>点击上传</el-button
				>
			</el-upload>
		</div>
		<el-scrollbar class="imgList">
			<img
				:src="item.value"
				:style="styleName"
				@click="handleSetimg(item.value)"
				v-for="(item, index) in imgOption[imgActive]"
				:key="index"
			/>
		</el-scrollbar>
	</el-dialog>
</template>

<script>
import fileService from "@/api/sys/fileService";
import { imgOption } from "@/datav/option/config";
export default {
	data() {
		return {
			imgVisible: false,
			imgObj: "",
			type: "",
			imgActive: 0,
			imgOption: imgOption,
			imgTabs: [],
		};
	},
	computed: {
		styleName() {
			if (this.type === "background") {
				return {
					width: "200px",
				};
			}
			return {};
		},
	},
	watch: {
		type: {
			handler() {
				if (this.type === "background") {
					this.imgActive = 0;
				} else if (this.type == "border") {
					this.imgActive = 1;
				} else {
					this.imgActive = 2;
				}
			},
			immediate: true,
		},
	},
	methods: {
		onSuccess(url) {
			this.imgOption[this.imgActive].unshift({
				label: url,
				value: url,
			});
		},
		openImg(item, type) {
			this.type = type;
			this.imgObj = item;
			this.imgVisible = true;
		},
		handleSetimg(item) {
			this.imgVisible = false;
			this.$emit("change", item, this.imgObj);
		},
		request(param) {
			const data = new FormData();
			data.append(param.filename, param.file);
			for (const key in param.data) {
				data.append(key, param.data[key]);
			}
			fileService
				.uploadFile(data, {
					onUploadProgress: (e) => {
						const complete = parseInt(
							((e.loaded / e.total) * 100) | 0,
							10
						);
						param.onProgress({ percent: complete });
					},
				})
				.then((res) => {
					param.onSuccess(res);
					// eslint-disable-next-line no-unused-vars
				})
				.catch(() => {
					// param.onError(err)
				});
		},
	},
};
</script>

<style></style>

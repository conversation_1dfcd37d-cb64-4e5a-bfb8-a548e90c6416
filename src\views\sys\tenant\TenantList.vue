<template>
	<div class="page">
		<el-form
			:inline="true"
			class="query-form m-b-10"
			v-if="searchVisible"
			ref="searchForm"
			:model="searchForm"
			@keyup.enter="refreshList()"
			@submit.prevent
		>
			<!-- 搜索框-->
			<el-form-item prop="name" label="租户名称：">
				<el-input
					v-model="searchForm.name"
					placeholder="租户名称"
					clearable
				></el-input>
			</el-form-item>
			<el-form-item prop="code" label="租户编码：">
				<el-input
					v-model="searchForm.code"
					placeholder="租户编码"
					clearable
				></el-input>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="refreshList()" icon="search"
					>查询</el-button
				>
				<el-button
					type="default"
					@click="resetSearch()"
					icon="refresh-right"
					>重置</el-button
				>
			</el-form-item>
		</el-form>
		<div class="jp-table">
			<vxe-toolbar
				ref="tenantToolbar"
				:refresh="{ query: refreshList }"
				export
				print
				custom
			>
				<template #buttons>
					<el-button
						v-if="hasPermission('sys:tenant:add')"
						type="primary"
						icon="plus"
						@click="add()"
						>新建</el-button
					>
					<el-button
						v-if="hasPermission('sys:tenant:edit')"
						type="warning"
						icon="edit-filled"
						@click="edit()"
						v-show="
							$refs.tenantTable &&
							$refs.tenantTable.getCheckboxRecords().length === 1
						"
						>修改</el-button
					>
					<el-button
						v-if="hasPermission('sys:tenant:del')"
						type="danger"
						icon="del-filled"
						@click="del()"
						v-show="
							$refs.tenantTable &&
							$refs.tenantTable.getCheckboxRecords().length > 0
						"
						>删除</el-button
					>
				</template>
				<template #tools>
					<vxe-button
						type="text"
						:title="searchVisible ? '收起检索' : '展开检索'"
						icon="vxe-icon-search"
						class="tool-btn"
						@click="searchVisible = !searchVisible"
					></vxe-button>
				</template>
			</vxe-toolbar>
			<div class="jp-table-body">
				<vxe-table
					border="inner"
					auto-resize
					height="auto"
					:loading="loading"
					size="small"
					ref="tenantTable"
					show-header-overflow
					show-overflow
					highlight-hover-row
					:menu-config="{}"
					:print-config="{}"
					:import-config="{}"
					:export-config="{}"
					@sort-change="sortChangeHandle"
					:sort-config="{ remote: true }"
					:data="dataList"
					:checkbox-config="{}"
				>
					<vxe-table-column width="60">
						<template #default="{ row }">
							<el-avatar
								:size="30"
								:style="{ background: row.color }"
							>
								{{ row.name.substring(0, 1) }}
							</el-avatar>
						</template>
					</vxe-table-column>
					<vxe-table-column
						type="checkbox"
						width="40px"
					></vxe-table-column>
					<vxe-table-column title="租户ID" field="id" sortable>
					</vxe-table-column>
					<vxe-table-column title="租户名称" field="name" sortable>
						<template #default="{ row }">
							<el-link
								type="primary"
								:underline="false"
								v-if="hasPermission('sys:tenant:edit')"
								@click="edit(row.id)"
								>{{ row.name }}</el-link
							>
							<el-link
								type="primary"
								:underline="false"
								v-else-if="hasPermission('sys:tenant:view')"
								@click="view(row.id)"
								>{{ row.name }}</el-link
							>
							<span v-else>{{ row.name }}</span>
						</template>
					</vxe-table-column>
					<vxe-table-column title="是否可用" field="status" sortable>
						<template #default="{ row }">
							<el-tag v-if="row.status === '1'">
								{{
									$dictUtils.getDictLabel(
										"yes_no",
										row.status,
										"-"
									)
								}}
							</el-tag>
							<el-tag v-else type="danger">
								{{
									$dictUtils.getDictLabel(
										"yes_no",
										row.status,
										"-"
									)
								}}
							</el-tag>
						</template>
					</vxe-table-column>
					<vxe-table-column
						title="租户开始日期"
						field="beginDate"
						sortable
					></vxe-table-column>
					<vxe-table-column
						title="租户结束日期"
						field="endDate"
						sortable
					></vxe-table-column>
					<vxe-table-column title="绑定域名" field="domain" sortable>
						<template #default="{ row }">
							<el-link
								type="primary"
								:underline="false"
								:href="`http://${row.domain}`"
								target="_blank"
								>{{ row.domain }}</el-link
							>
						</template>
					</vxe-table-column>
					<vxe-table-column align="center" title="操作">
						<template #default="{ row }">
							<el-button
								type="primary"
								text
								icon="view-filled"
								@click="view(row.id)"
								>查看</el-button
							>
							<el-button
								v-if="hasPermission('sys:tenant:edit')"
								type="primary"
								text
								icon="edit-filled"
								@click="edit(row.id)"
								>修改</el-button
							>
							<el-button
								v-if="hasPermission('sys:tenant:del')"
								type="danger"
								text
								icon="del-filled"
								@click="del(row.id)"
								>删除</el-button
							>
						</template>
					</vxe-table-column>
				</vxe-table>
				<vxe-pager
					background
					size="small"
					:current-page="tablePage.currentPage"
					:page-size="tablePage.pageSize"
					:total="tablePage.total"
					:page-sizes="[
						10,
						20,
						100,
						1000,
						{ label: '全量数据', value: -1 },
					]"
					:layouts="[
						'PrevPage',
						'JumpNumber',
						'NextPage',
						'FullJump',
						'Sizes',
						'Total',
					]"
					@page-change="currentChangeHandle"
				>
				</vxe-pager>
			</div>
		</div>
		<!-- 弹窗, 新增 / 修改 -->
		<TenantForm
			ref="tenantForm"
			@refreshDataList="refreshList"
		></TenantForm>
	</div>
</template>

<script>
import TenantForm from "./TenantForm";
import tenantService from "@/api/sys/tenantService";
export default {
	data() {
		return {
			searchVisible: true,
			searchForm: {
				name: "",
				code: "",
			},
			dataList: [],
			tablePage: {
				total: 0,
				currentPage: 1,
				pageSize: 10,
				orders: [{ column: "create_time", asc: false }],
			},
			loading: false,
		};
	},
	components: {
		TenantForm,
	},
	created() {
		this.initForm = JSON.stringify(this.inputForm);
	},
	mounted() {
		this.$nextTick(() => {
			// 将表格和工具栏进行关联
			const $table = this.$refs.tenantTable;
			const $toolbar = this.$refs.tenantToolbar;
			$table.connect($toolbar);
		});
		this.refreshList();
	},

	methods: {
		// 获取数据列表
		refreshList() {
			this.loading = true;
			tenantService
				.list({
					current: this.tablePage.currentPage,
					size: this.tablePage.pageSize,
					orders: this.tablePage.orders,
					...this.searchForm,
				})
				.then((data) => {
					this.dataList = data.records;
					this.tablePage.total = data.total;
					this.loading = false;
				});
		},
		// 当前页
		currentChangeHandle({ currentPage, pageSize }) {
			this.tablePage.currentPage = currentPage;
			this.tablePage.pageSize = pageSize;
			this.refreshList();
		},
		// 排序
		sortChangeHandle(column) {
			this.tablePage.orders = [];
			if (column.order != null) {
				this.tablePage.orders.push({
					column: this.$utils.toLine(column.property),
					asc: column.order === "asc",
				});
			} else {
				this.tablePage.orders = [{ column: "create_time", asc: false }];
			}
			this.refreshList();
		},
		// 新增
		add() {
			this.$refs.tenantForm.init("add", "");
		},
		// 修改
		edit(id) {
			id =
				id ||
				this.$refs.tenantTable.getCheckboxRecords().map((item) => {
					return item.id;
				})[0];
			// if (id === '10000') {
			//   this.$message.warning('禁止修改默认租户!')
			//   return
			// }
			this.$refs.tenantForm.init("edit", id);
		},
		// 查看
		view(id) {
			this.$refs.tenantForm.init("view", id);
		},
		// 删除
		del(id) {
			let ids =
				id ||
				this.$refs.tenantTable
					.getCheckboxRecords()
					.map((item) => {
						return item.id;
					})
					.join(",");
			if (`,${ids},`.indexOf(",10000,") >= 0) {
				this.$message.warning("禁止删除默认租户!");
				return;
			}
			this.$confirm(`确定删除所选项吗?`, "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
			}).then(() => {
				this.loading = true;
				tenantService.delete(ids).then((data) => {
					this.$message.success(data);
					this.refreshList();
					this.loading = false;
					if (this.$TOOL.data.get("IS_PRIMARY_TENANT")) {
						tenantService.list().then((data) => {
							this.$TOOL.data.set("TENANT_LIST", data.records);
						});
					}
				});
			});
		},
		resetSearch() {
			this.$refs.searchForm.resetFields();
			this.refreshList();
		},
	},
};
</script>

export default class BoundingBoxCoordinateConverter {
    /**
     * 创建基于四边形的坐标转换器
     * @param {Object} topLeft - 左上角(西北角)点
     * @param {Array} topLeft.geo - 左上角地理坐标 [经度, 纬度]
     * @param {Array} topLeft.local - 左上角本地坐标 [x, y]
     * @param {Object} topRight - 右上角(东北角)点
     * @param {Array} topRight.geo - 右上角地理坐标 [经度, 纬度]
     * @param {Array} topRight.local - 右上角本地坐标 [x, y]
     * @param {Object} bottomRight - 右下角(东南角)点
     * @param {Array} bottomRight.geo - 右下角地理坐标 [经度, 纬度]
     * @param {Array} bottomRight.local - 右下角本地坐标 [x, y]
     * @param {Object} bottomLeft - 左下角(西南角)点
     * @param {Array} bottomLeft.geo - 左下角地理坐标 [经度, 纬度]
     * @param {Array} bottomLeft.local - 左下角本地坐标 [x, y]
     */
    constructor(topLeft, topRight, bottomRight, bottomLeft) {
        // 验证输入
        if (
            !this.isValidQuadrilateral(topLeft.geo, topRight.geo, bottomRight.geo, bottomLeft.geo)
        ) {
            throw new Error("无效的四边形边界坐标");
        }

        // 存储基准点
        this.topLeftGeo = topLeft.geo;
        this.topLeftLocal = topLeft.local;
        this.topRightGeo = topRight.geo;
        this.topRightLocal = topRight.local;
        this.bottomRightGeo = bottomRight.geo;
        this.bottomRightLocal = bottomRight.local;
        this.bottomLeftGeo = bottomLeft.geo;
        this.bottomLeftLocal = bottomLeft.local;

        // 地球半径（米），用于距离计算
        this.EARTH_RADIUS = 6371000;
    }

    /**
     * 验证四边形坐标是否有效
     * @param {Array} tl - 左上角地理坐标 [经度, 纬度]
     * @param {Array} tr - 右上角地理坐标 [经度, 纬度]
     * @param {Array} br - 右下角地理坐标 [经度, 纬度]
     * @param {Array} bl - 左下角地理坐标 [经度, 纬度]
     * @returns {boolean} 是否有效
     */
    isValidQuadrilateral(tl, tr, br, bl) {
        // 简单验证：确保四个点不共线
        const area = this.calculatePolygonArea([tl, tr, br, bl]);
        return area > 0;
    }

    /**
     * 计算多边形面积（使用鞋带公式）
     * @param {Array} points - 多边形顶点坐标数组
     * @returns {number} 面积
     */
    calculatePolygonArea(points) {
        let area = 0;
        const n = points.length;
        for (let i = 0; i < n; i++) {
            const j = (i + 1) % n;
            area += points[i][0] * points[j][1];
            area -= points[j][0] * points[i][1];
        }
        return Math.abs(area) / 2;
    }

    /**
     * 将经纬度转换为相对坐标
     * 这里使用双线性插值进行近似转换
     * @param {Array} geoCoord - 地理坐标 [经度, 纬度]
     * @returns {Array} 相对坐标 [x, y]
     */
    geoToLocal(geoCoord) {
        // 实现双线性插值转换逻辑
        // 此处为简化示例，实际应用可能需要更精确的算法
        const [lon, lat] = geoCoord;
        const [tlLon, tlLat] = this.topLeftGeo;
        const [trLon, trLat] = this.topRightGeo;
        const [brLon, brLat] = this.bottomRightGeo;
        const [blLon, blLat] = this.bottomLeftGeo;

        const s = (lon - blLon) / (brLon - blLon);
        const t = (lat - blLat) / (tlLat - blLat);

        const [tlX, tlY] = this.topLeftLocal;
        const [trX, trY] = this.topRightLocal;
        const [brX, brY] = this.bottomRightLocal;
        const [blX, blY] = this.bottomLeftLocal;

        const x = (1 - s) * (1 - t) * blX + s * (1 - t) * brX + (1 - s) * t * tlX + s * t * trX;
        const y = (1 - s) * (1 - t) * blY + s * (1 - t) * brY + (1 - s) * t * tlY + s * t * trY;

        return [x, y];
    }

    /**
     * 将相对坐标转换为经纬度
     * 这里使用双线性插值进行近似转换
     * @param {Array} localCoord - 相对坐标 [x, y]
     * @returns {Array} 地理坐标 [经度, 纬度]
     */
    localToGeo(localCoord) {
        // 实现双线性插值转换逻辑
        // 此处为简化示例，实际应用可能需要更精确的算法
        const [x, y] = localCoord;
        const [tlX, tlY] = this.topLeftLocal;
        const [trX, trY] = this.topRightLocal;
        const [brX, brY] = this.bottomRightLocal;
        const [blX, blY] = this.bottomLeftLocal;

        // 求解双线性插值的逆问题，这里简化处理
        const s = (x - blX) / (brX - blX);
        const t = (y - blY) / (tlY - blY);

        const [tlLon, tlLat] = this.topLeftGeo;
        const [trLon, trLat] = this.topRightGeo;
        const [brLon, brLat] = this.bottomRightGeo;
        const [blLon, blLat] = this.bottomLeftGeo;

        const lon =
            (1 - s) * (1 - t) * blLon + s * (1 - t) * brLon + (1 - s) * t * tlLon + s * t * trLon;
        const lat =
            (1 - s) * (1 - t) * blLat + s * (1 - t) * brLat + (1 - s) * t * tlLat + s * t * trLat;

        return [lon, lat];
    }

    /**
     * 计算两点之间的实际距离（米）
     * @param {Array} geoCoord1 - 第一个点的地理坐标 [经度, 纬度]
     * @param {Array} geoCoord2 - 第二个点的地理坐标 [经度, 纬度]
     * @returns {number} 距离（米）
     */
    calculateDistance(geoCoord1, geoCoord2) {
        const [lon1, lat1] = geoCoord1;
        const [lon2, lat2] = geoCoord2;

        const dLat = ((lat2 - lat1) * Math.PI) / 180;
        const dLon = ((lon2 - lon1) * Math.PI) / 180;
        const a =
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos((lat1 * Math.PI) / 180) *
                Math.cos((lat2 * Math.PI) / 180) *
                Math.sin(dLon / 2) *
                Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return this.EARTH_RADIUS * c;
    }

    /**
     * 获取当前转换器的精度评估（局部坐标1单位对应的米数）
     * @returns {Object} 包含x方向和y方向的精度
     */
    getPrecision() {
        // 计算左上和右下角之间的实际距离
        const geoDistanceX = this.calculateDistance(this.topLeftGeo, [
            this.bottomRightGeo[0],
            this.topLeftGeo[1],
        ]);
        const geoDistanceY = this.calculateDistance(this.topLeftGeo, [
            this.topLeftGeo[0],
            this.bottomRightGeo[1],
        ]);

        // 计算局部坐标1单位对应的米数
        const precisionX = geoDistanceX / this.localWidth;
        const precisionY = geoDistanceY / this.localHeight;

        return {
            x: precisionX,
            y: precisionY,
            unit: "meters per local unit",
        };
    }
}

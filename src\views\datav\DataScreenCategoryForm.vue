<template>
<div>
  <v-dialog
    :title="title"
    :close-on-click-modal="false"
    v-model="visible">
    <el-form  :model="inputForm" ref="inputForm" v-loading="loading" :class="method==='view'?'readonly':''"  :disabled="method==='view'"
             label-width="120px">
      <el-row  :gutter="15">
        <el-col :span="24">
            <el-form-item label="上级目录" prop="parent.id"
                :rules="[
                 ]">
                <el-tree-select      
                  ref="parent"
                  value-key="id" 
                  :props="{
                    label: 'name',         // 显示名称
                    children: 'children'    // 子级字段名
                  }"
                  default-expand-all
                  :data="dataScreenCategoryTreeData"
                  v-model="inputForm.parent.id"
                  check-strictly 
                  style="width:100%"
                  :clearable="true" 
                  :accordion="true" />
           </el-form-item>
        </el-col>
        <el-col :span="24">
            <el-form-item label="名称" prop="name"
                :rules="[
                  {required: true, message:'名称不能为空', trigger:'blur'}
                 ]">
              <el-input v-model="inputForm.name" placeholder="请填写名称"     ></el-input>
           </el-form-item>
        </el-col>
        <el-col :span="24">
            <el-form-item label="排序" prop="sort"
                :rules="[
                  {required: true, message:'排序不能为空', trigger:'blur'},
                  {validator: validator.isNumber, trigger:'blur'}
                 ]">
              <el-input-number v-model="inputForm.sort" placeholder="请填写排序"  :step="10"  :min="0" :max="99999" style="width: 100%;"  ></el-input-number>
           </el-form-item>
        </el-col>
        <el-col :span="24">
            <el-form-item label="备注信息" prop="remarks"
                :rules="[
                 ]">
          <el-input type="textarea" v-model="inputForm.remarks" placeholder="请填写备注信息"     ></el-input>
           </el-form-item>
        </el-col>
        </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button  @click="visible = false" icon="circle-close">关闭</el-button>
        <el-button  type="primary" v-if="method != 'view'" @click="doSubmit()" icon="circle-check" v-noMoreClick>确定</el-button>
      </span>
    </template>
  </v-dialog>
</div>
</template>

<script>
  import dataScreenCategoryService from '@/api/datav/dataScreenCategoryService'
  export default {
    data () {
      return {
        title: '',
        method: '',
        visible: false,
        loading: false,
        dataScreenCategoryTreeData: [],
        inputForm: {
          id: '',
          name: '',
          remarks: '',
          parent: {
            id: ''
          },
          sort: ''
        }
      }
    },
    methods: {
      init (method, obj) {
        this.method = method
        this.inputForm.id = obj.id
        if (method === 'add') {
          this.title = '新建大屏分类'
        } else if (method === 'addChild') {
          this.title = '添加下级大屏分类'
        } else if (method === 'edit') {
          this.title = '修改大屏分类'
        } else if (method === 'view') {
          this.title = '查看大屏分类'
        }
        dataScreenCategoryService.treeData(this.inputForm.id).then((data) => {
          this.dataScreenCategoryTreeData = data
        })
        this.visible = true
        this.loading = false
        this.$nextTick(() => {
          this.$refs.inputForm.resetFields()
          this.inputForm.parent.id = obj.parent.id
          this.inputForm.parent.name = obj.parent.name
          if (method === 'edit' || method === 'view') { // 修改或者查看
            this.loading = true
            dataScreenCategoryService.queryById(this.inputForm.id).then((data) => {
              this.inputForm = this.recover(this.inputForm, data)
              this.loading = false
            })
          }
        })
      },
      // 表单提交
      doSubmit () {
        this.$refs['inputForm'].validate((valid) => {
          if (valid) {
            this.loading = true
            dataScreenCategoryService.save(this.inputForm).then((data) => {
              this.loading = false
              this.visible = false
              this.$message.success(data)
              this.$emit('refreshDataList')
            }).catch(() => {
              this.loading = false
            })
          }
        })
      }
    }
  }
</script>
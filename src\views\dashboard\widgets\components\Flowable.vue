<template>
	<el-card shadow="never" style="height: 100%">
		<el-row class="myMods">
			<el-col :span="6">
				<div class="step-wrapper">
					<router-link :to="{ path: '/flowable/task/ApplyList' }">
						<img
							class="step-image"
							src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABQCAMAAAC5zwKfAAABGlBMVEUAAAAAAP8AgP8Aqv8AgP8zmf8qgP8ggP8Xi/8qlf8iiP8gj/8cgPEdif8kif8ikP8hjPchjP8ni/cjh/gjjvgmjPkji/kiiPkijv8jjPoiifoki/sjivgjiPgki/gkifgjifwlivkkjPwkifolivwji/oljPolivojivski/skifkki/kjjPkli/kkivokivokifglivolifkli/oki/oki/kkivskivklivkki/kjivoli/ojivojifgji/gji/okivokivkkivkkivkkivkkivkkivkki/okivkkivkkivkkivgkivgkivkjivkkivokivokifkkivojivkkivkkivokivkkivojivkjivkkivkkivkki/okivmdXVYCAAAAXXRSTlMAAQIDBAUGCAsMDxASGhweHx8hJCQoLC0tMzQ5SElNTlBTVF1gZWZvdHmAhomKjI6PkqCgoaKqrK64u8HCw8PDxMjL09TW19vi4+Tq6+3v7/Dz9Pb39/r6/P39/v7dYDghAAACJElEQVRYw+2YWTMDQRSF7zDELrGMMIg1CGaQCCJ2IvYlCYlx///f8KB40Hd6breOKlU5r3Pqq5ma7nMXgJZa+ifqijuu68S7TLB6Un6hHCAiIgblgp/q+Q2tN12s4w/Vi+leTdxotoakatlRDdxwvoGhauSHFXGxTBWlqmZiKrxkCSNVSvJ5ixVkqLLIxFkeMuVZHJ6dQ7ZyNuP9tlFBueh39FBJXuT/QEVF/JlkRRVYkZ6eWAmVVZKd8AxqKCO5v1UdYDX8XudRS/nQvGroARthaZal3I9LE/NnEcRsSD6TeToJAPbykxRYozM8TXmvP58N7EiJaRJ4SFnPv55OXkqARbK+1aVAaYjXqVqYQjkQYGQvlJgigH4kEGDuKgToE8ACAwjd3itpKxDAMgcIMHZE2cpE/xLwgGAt3Ii2QOx7EsgEAvTtir6E4HL4QGi7EHyOYHIVgLAm+NzfAbcYQJVPHn9mfHKcD5x7FH1x/WPTufnOOjbcgz10yjzYvKsHM/fsq8cJhw4/4IdDdHzB4IlKfEUFLMD0nVLAQlEOtNcDtRJAF6lv4MCxcpEiy+gXcOpWvYyShf7BAgBoX33TKPR0KzIPAP37eq0I2Sy9bMyu3Go2S+bbOeMNp/mW2HjTbn6sMD/4gJVT4TFGM7AVhr1txvBofrw1P4A3YUVgfolhfs3ShEXQ56rqQFxVHWivqpqwTPvuexKO6zoJI+u+llr6C30AImnPnDDxrmkAAAAASUVORK5CYII="
							alt=""
						/>
						<p>我发起的</p>
					</router-link>
				</div>
			</el-col>
			<el-col :span="6">
				<div class="step-wrapper">
					<router-link :to="{ path: '/flowable/task/HistoryList' }">
						<img
							class="step-image"
							src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABQCAMAAAC5zwKfAAACFlBMVEUAAAAAAP8A//8AgP8Aqv+qqv8AgP+Av/8Amf+ZzP8qgP8ggP+fv/+Oxv8agP8Xi/8ui+iL0f8VgOoqlf8niesnif+Stu0gj/8eh/+Oxv+PwvUnif8mhPYkifYiiPciiP8hjP8oh/coj/+Pw/gjjvgiivghifgkjPkji/mRxfmQyP8kh/okjfokivqRw/qQw/okifoki/Yki/sjiPsjjfsjivsijPuQw/sljPuQwfski/cki/sli/sljPuTxPySw/wkifgjifwlivkjiPkmi/mSw/wkifojivojjPojivolivqQxPqQwvokifqRw/qSxPojivokivgki/ski/uQw/uRw/uSxPskivkjifkli/kkifkki/uRwvmRxPuTxPskivsjivkli/kjivgjivokivoki/okivoki/qQw/oki/iRw/okivokivkkivqRxPwli/kki/kkivmRwvskivkkivkkivmSwvskivmRw/skivmRw/skifkkivkkivojivokivgkivoki/oki/oji/okivoki/okivokivokifkki/kki/ojivokivkkivkkivokivkkivkkivkjifkkivkkivkkivkkivkkivkjivkkifokivokivgkivokifgkivoli/ojivokivokivokivkkivoki/ojivojivkkivkkivkkivokivkkivokivkjivkkivkki/okivmSNfP3AAAAsXRSTlMAAQECAwMEBAUFBggICQoLCwsMDA0NDhAREhkaGxweHh8gICIkJScqLCwuMTEyMzc4OTk6Ojs8PD4+QEBERUlNTlBTWFhZXV5fYGBjZWprbG1xcnd4e36BgoSGhoaGhoeJipCQlJWWlpqcnKSlpaWnqaqsr7CxsbKys7O2t7q7vr+/wcPExcbP0NDQ0dLT09fa3t/h4uPk5efo6Onp6urt7u7v8fHx9fb29/f4+Pr8/v7IYf4CAAACFElEQVRYw2NgGAWjYBQMGsCq5xMaG4cXhPlZ8hJtnn5GFTEgz55I81wrqogEIezEmGdXRTzwJ8I8ySISDKzSImygNynmVcUQNjCLJAPLxAiZx1NFGtAkZKAUiQaaEzKQU5k0IDgM8jGXqpkVscBCXYCQcRq5SzaSAtbU2OAzjjF83UaSQT4fbgPTNpIDmrhwmee+kTyQjMM87mlkGrhWBbuBjhvJBRHYDUwh28A27AaWkm3gVOwGNpBt4FLsBjYSqX1KyzxqGrjAgYGBzXct1QxcbQJWG0gtAze4Q9PsMioZGA9TPIE6BpYwM1DVhV1CMLXUCcOZ8jClJqvJM3DhImTeKiOYSoVZZKXDDkMGBoN2RAS7wRQKd5OVsCeLgwSFamH8aJg65lLysl4QtNIqgHCLmWDqEsjMy7YwB6WDeJ38MGUeG8g00BMuEbFx4ww5GMd0NbmlTQvcjwwBKwxgTMU55BdfqQgTZWEMkR5KysMcDnQlzOWUFbDV6HV4IqUldqsEigovyquAPmkkBWarqVCnTFKFyyvNpUolNVsXKi3aS6Vab7E1JIIrqVaNrnQBZelMatbLzVFJE2lY0Q+cgXVkGzgfu4HZZBvYj93AYLINLMBuoNp6cg10xtHILiDXxyw4DJSZTpZ5q41x9lN0yDFxuROerpRMAcnh2KyNv7OnFpxT10gsqC+MNBgdNhsFo2AwAAAtOPG9B304zAAAAABJRU5ErkJggg=="
							alt=""
						/>
						<p>已办事项</p>
					</router-link>
				</div>
			</el-col>
			<el-col :span="6">
				<div class="step-wrapper">
					<router-link
						:to="{ path: '/flowable/extension/FlowCopyList' }"
					>
						<img
							class="step-image"
							src="data:image/png;base64,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"
							alt=""
						/>
						<p>抄送我的</p>
					</router-link>
				</div>
			</el-col>
			<el-col :span="6">
				<div class="step-wrapper">
					<router-link :to="{ path: '/flowable/task/ProcessList' }">
						<img
							class="step-image"
							src="data:image/png;base64,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"
							alt=""
						/>
						<p>发起流程</p>
					</router-link>
				</div>
			</el-col>
		</el-row>
	</el-card>
</template>

<script>
export default {
	title: "工作流",
	icon: "liucheng1",
	description: "工作流常用菜单",
	layout: {
		w: 8,
		h: 4,
	},
	data() {
		return {};
	},
};
</script>

<style scoped>
.myMods {
	list-style: none;
	margin: -10px;
}
.myMods li {
	display: inline-block;
	width: 100px;
	height: 100px;
	vertical-align: top;
	transition: all 0.3s ease;
	margin: 10px;
	border-radius: 5px;
}
.myMods li:hover {
	opacity: 0.8;
	background: #f4f6f9;
}
.myMods li a {
	width: 100%;
	height: 100%;
	padding: 10px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	text-align: center;
	color: #fff;
}
.myMods li i {
	font-size: 26px;
	color: #248af9;
}
.myMods li p {
	font-size: 12px;
	color: #248af9;
	margin-top: 10px;
	width: 100%;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.modItem-add {
	border: 1px dashed #ddd;
	cursor: pointer;
}
.modItem-add i {
	font-size: 30px;
	color: #999 !important;
}
.modItem-add:hover,
.modItem-add:hover i {
	border-color: #409eff;
	color: #409eff !important;
}
.step-wrapper {
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	width: 120px;
	height: 120px;
	margin: 0 36px;
	border-radius: 12px;
	cursor: pointer;
}
.step-wrapper a p {
	font-size: 14px;
}
.step-wrapper:hover {
	background: #f4f6f9;
}
.step-image {
	width: 40px;
	height: 40px;
	margin-bottom: 16px;
}
.item-background {
	height: 100%;
}
.item-background p {
	color: #999;
	margin-top: 10px;
	line-height: 1.8;
}
</style>

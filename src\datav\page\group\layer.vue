<template>
	<draggable
		ghost-class="menu__ghost"
		class="menu_ul"
		:group="{ name: 'layer' }"
		v-model="nav2"
		item-key="index"
	>
		<template #item="{ element }">
			<div
				:key="element.index"
				class="menu__folder"
				:ref="common.NAME + element.index"
				@click.stop="contain.activeIndex = element.index"
				@mouseover.stop="mouseOver(element)"
				@mouseout.stop="mouseOut(element)"
				v-if="element.children"
			>
				<div
					@dblclick="handleChangeName(element)"
					v-contextmenu="{
						id: contain.menuId,
						event: contain.handleContextMenu,
						value: item,
					}"
					:class="[
						'menu__item--folder',
						{ 'is-active': contain.activeIndex == element.index },
					]"
				>
					<i
						class="iconfont icon-fold"
						@click="openFolder(element)"
						:class="{ 'is-active': element.menu }"
					></i>
					<i
						class="iconfont icon-folder"
						@click="handleSetActive(element)"
					></i>
					<input
						type="text"
						@keyup.enter="element.isname = false"
						v-if="element.isname"
						v-model="element.name"
					/>
					<span v-else class="menu__name">{{ element.name }}</span>
					<span class="menu__menu">
						<i
							class="el-icon-view"
							:class="{ 'is-active': element.display !== true }"
							@click.stop="
								contain.handleParams('display', element)
							"
						></i>
						<i
							class="el-icon-lock"
							:class="{ 'is-active': element.lock === true }"
							@click.stop="contain.handleParams('lock', element)"
						></i>
					</span>
				</div>
				<div
					:key="'list' + element.index"
					class="menu__list"
					v-show="element.menu"
				>
					<layer
						:count="count + 1"
						:key="element.index"
						:nav="element.children"
					></layer>
				</div>
			</div>
			<div
				v-else
				:key="element.index"
				v-contextmenu="{
					id: contain.menuId,
					event: contain.handleContextMenu,
					value: element,
				}"
				@click.stop="handleSetActive(element)"
				:class="[
					'menu__item',
					{
						'is-active': handleGetActive(element),
						'is-over': contain.activeOverIndex === element.index,
					},
				]"
				@mouseover.stop="mouseOver(element)"
				@mouseout.stop="mouseOut(element)"
			>
				<span class="menu__icon">
					<i :class="'iconfont ' + element.icon"></i>
				</span>
				<span class="menu__label"> {{ element.name }}</span>
				<span class="menu__menu">
					<i
						class="el-icon-view"
						:class="{ 'is-active': element.display !== true }"
						@click.stop="contain.handleParams('display', element)"
					></i>
					<i
						class="el-icon-lock"
						:class="{ 'is-active': element.lock === true }"
						@click.stop="contain.handleParams('lock', element)"
					></i>
				</span>
			</div>
		</template>
	</draggable>
</template>

<script>
import vuedraggable from "vuedraggable/src/vuedraggable";
import common from "@/datav/config";
export default {
	name: "layer",
	inject: ["contain"],
	provide() {
		return {
			contain: this.contain,
			nav2: this.nav,
		};
	},
	components: {
		draggable: vuedraggable,
	},
	props: {
		count: {
			type: Number,
			default: 1,
		},
		nav: {
			type: Array,
			default: () => {
				return [];
			},
		},
	},
	data() {
		return {
			common,
		};
	},
	methods: {
		mouseOver(item) {
			if (item.children) {
				this.contain.$refs.container
					.getItemRef(item.index)
					.setActive(true);
			} else {
				this.contain.activeOverIndex = item.index;
			}
		},
		mouseOut(item) {
			if (item.children) {
				this.contain.$refs.container
					.getItemRef(item.index)
					.setActive(false);
			} else {
				this.contain.activeOverIndex = undefined;
			}
		},
		handleGetActive(item) {
			return this.contain.active.includes(item.index);
		},
		handleSetActive(item) {
			if (item.children) {
				let active = [];
				const deepList = (list) => {
					list.forEach((ele) => {
						if (ele.children) deepList(ele.children);
						else active.push(ele.index);
					});
				};
				deepList(item.children);
				this.contain.selectNav(active);
			} else {
				this.contain.selectNav(item.index);
			}
		},
		handleChangeName(item) {
			item.isname = !item.isname;
		},
		openFolder(item) {
			item.menu = !item.menu;
			item.isname = false;
		},
	},
};
</script>

<style></style>

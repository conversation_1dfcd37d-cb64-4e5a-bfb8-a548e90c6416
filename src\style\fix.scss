/* 覆盖element-plus样式 */
:root {
    --el-text-color-primary: #4e5969;
    // --el-font-family: Inter,"-apple-system",BlinkMacSystemFont,"PingFang SC","Hiragino Sans GB","noto sans","Microsoft YaHei","Helvetica Neue",Helvetica,Arial,sans-serif
}

.el-menu {border: none!important;}
.el-menu .el-menu-item a {color: inherit;text-decoration: none;display: block;width:100%;height:100%;position: absolute;top:0px;left:0px;}
.el-form-item-msg {font-size: 12px;color: #999;clear: both;}
.adminui-topbar+.el-container.jp-container {
    height: calc(100% - 103px);
    padding: 12px 12px 8px 12px;
}
.el-container.jp-container {
    height: 100%;
    .el-header {
        background: #fff;border-bottom: 1px solid #e6e6e6;display: flex;justify-content: space-between;align-items: center;
        .left-panel {display: flex;align-items: center;}
        .right-panel {display: flex;align-items: center;}
        .right-panel > * + * {margin-left:10px;}
    

    }
    // .el-header {background: #fff;border-bottom: 1px solid var(--el-border-color-light);padding:13px 15px;display: flex;justify-content: space-between;align-items: center;}
    .el-aside {border-right: 1px solid #e6e6e6;background: #fff;}
    .el-main {padding:15px;flex-basis: 100%;}
    .el-main.nopadding {padding:0;background: #fff;}
    .el-footer {background: #fff;border-top: 1px solid #e6e6e6;padding:13px 15px;}
}




.el-main > .scTable .el-table--border {border: 0;}
.el-drawer__body {overflow: auto;padding:0;}
.el-popconfirm__main {margin: 14px 0;}
.el-card__header {border-bottom: 0;font-size: 17px;font-weight: bold;padding:15px 20px 0px 20px;}
.el-dialog__title {font-size: 17px;font-weight: bold;}
.el-drawer__header>:first-child {font-size: 17px;font-weight: bold;}
.el-tree.menu .el-tree-node__content {height:36px;}
.el-tree.menu .el-tree-node__content .el-tree-node__label .icon {margin-right: 5px;}
.el-progress__text {font-size: 12px!important;}
.el-progress__text i {font-size: 14.4px!important;}
.el-step.is-horizontal .el-step__line {height:1px;}
.el-step__title {font-size: 14px;}
.drawerBG {background: #f6f8f9;}
.el-button+.el-dropdown {margin-left: 10px;}
.el-button-group+.el-dropdown {margin-left: 10px;}
.el-tag+.el-tag {margin-left: 10px;}
.el-button-group+.el-button-group {margin-left: 10px;}
.el-tabs__nav-wrap::after {height: 1px;}
.el-table th.is-sortable {transition: .1s;}
.el-table th.is-sortable:hover {background: #eee;}
.el-table .el-table__body-wrapper {background: #f6f8f9;}


.el-menu {
    --el-menu-text-color: var(--el-text-color-primary);
}

// .aminui-side .el-menu-item.is-active {background: var(--el-color-primary-light-9);color: var(--el-color-primary)!important;}


// .aminui-side-split li.active {background-color: #fff; color:var(--el-color-primary) !important;}
.contextmenu li:hover {background-color: var(--el-color-primary-light-9)!important;color: var(--el-color-primary-light-2)!important;}
.data-box .item-background {background-color: var(--el-color-primary)!important;}
.layout-setting,.diy-grid-setting {background-color: var(--el-color-primary)!important;}

/* 覆盖tinymce样式 */
.sceditor .tox-tinymce {border: 1px solid #DCDFE6;}
.sceditor .tox .tox-statusbar {border-top: 1px solid #DCDFE6;}
.sceditor .tox .tox-toolbar__primary {background: #f6f8f9;border-bottom: 1px solid #DCDFE6;}

.vxe-cell, .cell{
    .el-button.is-text{
        background: 0 0;
        padding-left: 0;
        padding-right: 0;
    }
}
.splitpanes.default-theme .splitpanes__pane {
    background-color: transparent !important;
}

.splitpanes {
    padding: 8px 12px;
}
.adminui-topbar+.splitpanes {
  padding: 12px 12px 8px 12px;
  height: calc(100% - 103px);
}
.detail{
    padding: 20px;
    .el-descriptions__table {
      td.el-descriptions__label {
          width: 200px;
        }
    }
  }
    
.vue-grid-item>.vue-resizable-handle {
 z-index: 300; 
}



  /* 按钮 default */
  .el-button--default:not(.is-text):not(.el-button--text),
  .el-button--default:not(.is-text):not(.el-button--text):focus {
    border: 1px solid var(--el-color-primary);
    font-family: PingFangSC-Regular;
    color: var(--el-color-primary);
    font-weight: 400;
    background: #ffffff;
  }
  .el-button--default:not(.is-text):not(.el-button--text):focus {
    -webkit-box-shadow: 0 0 0 2px var(--el-color-primary-focus);
    box-shadow: 0 0 0 2px var(--el-color-primary-focus);
}
  .el-button--default:not(.is-text):not(.el-button--text):hover {
    background: var(--el-color-primary-light-9);
    border: 1px solid var(--el-color-primary);
    font-family: PingFangSC-Regular;
    color: var(--el-color-primary);
    font-weight: 400;
  }

    /* 按钮 primary */
  .el-button--primary:not(.is-text):not(.el-button--text),
  .el-button--primary:not(.is-text):not(.el-button--text):focus {
    background: var(--el-color-primary);
    border: 1px solid var(--el-color-primary);
    font-family: PingFangSC-Regular;
    color: #ffffff;
    letter-spacing: 0;
    font-weight: 400;
    display: inline-flex;
    justify-content: center;
    align-items: center;
  }
  .el-button--primary:not(.is-text):not(.el-button--text):focus {
      -webkit-box-shadow: 0 0 0 2px var(--el-color-primary-focus);
      box-shadow: 0 0 0 2px var(--el-color-primary-focus);
  }
  .el-button--primary:not(.is-text):not(.el-button--text):hover {
    background-color: var(--el-color-primary-light-2);
    color: #ffffff;
    border: 1px solid var(--el-color-primary-light-2);
  }

  .el-button--primary:not(.is-text):not(.el-button--text).is-disabled,
  .el-button--primary:not(.is-text):not(.el-button--text).is-disabled:active,
  .el-button--primary:not(.is-text):not(.el-button--text).is-disabled:focus,
  .el-button--primary:not(.is-text):not(.el-button--text).is-disabled:hover {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
  }


    /* 按钮 danger */
    .el-button--danger:not(.is-text):not(.el-button--text),
    .el-button--danger:not(.is-text):not(.el-button--text):focus {
      border: 1px solid #f16643;
      font-family: PingFangSC-Regular;
      color: #f16643;
      font-weight: 400;
      background: #ffffff;
    }
    .el-button--danger:not(.is-text):not(.el-button--text):focus {
        -webkit-box-shadow: 0 0 0 2px rgb(237 64 20 / 20%);
        box-shadow: 0 0 0 2px rgb(237 64 20 / 20%);
    }
    .el-button--danger:not(.is-text):not(.el-button--text):hover {
      border: 1px solid #f16643;
      font-family: PingFangSC-Regular;
      color: #f16643;
      background: rgba(254,245,243,.5);
      font-weight: 400;
    }


    .el-button--danger:not(.is-text):not(.el-button--text).is-disabled,
    .el-button--danger:not(.is-text):not(.el-button--text).is-disabled:active,
    .el-button--danger:not(.is-text):not(.el-button--text).is-disabled:focus,
    .el-button--danger:not(.is-text):not(.el-button--text).is-disabled:hover {
      color: var(--el-color-danger-light-5);
      background-color: #ffffff;
      border-color: var(--el-color-danger-light-8);
    }

    /* 按钮 warning */
    .el-button--warning:not(.is-text):not(.el-button--text),
    .el-button--warning:not(.is-text):not(.el-button--text):focus {
      border: 1px solid #f90;
      font-family: PingFangSC-Regular;
      color: #f90;
      font-weight: 400;
      background: #ffffff;
    }
    .el-button--warning:not(.is-text):not(.el-button--text):focus {
      -webkit-box-shadow: 0 0 0 2px rgb(255 153 0 / 20%);
      box-shadow: 0 0 0 2px rgb(255 153 0 / 20%);
    }
    .el-button--warning:not(.is-text):not(.el-button--text):hover {
      color: #ffad33;
      background: rgba(255,250,242,.5);
      border: 1px solid #f90;
      font-family: PingFangSC-Regular;
      color: #f90;
      font-weight: 400;
    }

    .el-button--warning:not(.is-text):not(.el-button--text).is-disabled,
    .el-button--warning:not(.is-text):not(.el-button--text).is-disabled:active,
    .el-button--warning:not(.is-text):not(.el-button--text).is-disabled:focus,
    .el-button--warning:not(.is-text):not(.el-button--text).is-disabled:hover {
      color: var(--el-color-warning-light-5);
      background-color: #ffffff;
      border-color: var(--el-color-warning-light-8);
    }

    /* 按钮 success */
    .el-button--success:not(.is-text):not(.el-button--text),
    .el-button--success:not(.is-text):not(.el-button--text):focus {
      border: 1px solid #19be6b;
      font-family: PingFangSC-Regular;
      color: #19be6b;
      font-weight: 400;
      background: #ffffff;
    }
    .el-button--success:not(.is-text):not(.el-button--text):focus {
      -webkit-box-shadow: 0 0 0 2px rgb(25 190 107 / 20%);
      box-shadow: 0 0 0 2px rgb(25 190 107 / 20%);
    }
    .el-button--success:not(.is-text):not(.el-button--text):hover {
      border: 1px solid #19be6b;
      font-family: PingFangSC-Regular;
      color: #19be6b;
      background: rgba(244,252,248,.5);
      font-weight: 400;
    }
    .el-button--success:not(.is-text):not(.el-button--text).is-disabled,
    .el-button--success:not(.is-text):not(.el-button--text).is-disabled:active,
    .el-button--success:not(.is-text):not(.el-button--text).is-disabled:focus,
    .el-button--success:not(.is-text):not(.el-button--text).is-disabled:hover {
      color: var(--el-color-success-light-5);
      background-color: #ffffff;
      border-color: var(--el-color-success-light-8);
    }

  /* 表格 */
  .el-table th {
    background: #f5f7f9 !important;
  }

  .el-table th .cell {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    letter-spacing: 0;
    font-weight: 550;
  }
  .el-form-item__label {
    font-family: PingFangSC-Regular;
    letter-spacing: 0;
    font-weight: 400;
  }
  .el-date-table td,
  .el-table .cell {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    letter-spacing: 0;
    font-weight: 400;
  }

  .el-sub-menu .el-icon {
    vertical-align: middle;
    margin-right: 5px;
    width: var(--el-menu-icon-width);
    text-align: center;
    font-size: 14px;
}

.el-icon{
  width: 0.9em;
  height: 0.9em;
  line-height: 0.9em;
}

.el-tag.el-tag--primary {
  background: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary)!important;
}


.el-tag.el-tag--danger {
  background: #fff1f0;
  border-color: #ffa39e;
  color: #f5222d!important;
}

.el-tag.el-tag--success {
  color: #52c41a!important;
  background: #f6ffed;
  border-color: #b7eb8f;
}

.el-tag.el-tag--warning {
  color: #faad14!important;
  background: #fffbe6;
  border-color: #ffe58f;
}
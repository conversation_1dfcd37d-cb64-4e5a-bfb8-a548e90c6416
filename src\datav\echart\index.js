import EchartBar from "./packages/bar/index.vue";
import EchartPie from "./packages/pie/index.vue";
import EchartLine from "./packages/line/index.vue";
import EchartTable from "./packages/table/index.vue";
import EchartRectangle from "./packages/rectangle/index.vue";
import EchartFlop from "./packages/flop/index.vue";
import EchartDatetime from "./packages/datetime/index.vue";
import EchartText from "./packages/text/index.vue";
import EchartSwiper from "./packages/swiper/index.vue";
import EchartIframe from "./packages/iframe/index.vue";
import EchartVideo from "./packages/video/index.vue";
import EchartWordCloud from "./packages/wordCloud/index.vue";
import EchartGauge from "./packages/gauge/index.vue";
import EchartProgress from "./packages/progress/index.vue";
import EchartMaps from "./packages/map/index.vue";
import EchartImg from "./packages/img/index.vue";
import EchartImgBorder from "./packages/imgBorder/index.vue";
import EchartTabs from "./packages/tabs/index.vue";
import EchartPictorialBar from "./packages/pictorialBar/index.vue";
import EchartRadar from "./packages/radar/index.vue";
import EchartFunnel from "./packages/funnel/index.vue";
import EchartScatter from "./packages/scatter/index.vue";
import EchartCommon from "./packages/common/index.vue";
import EchartDatav from "./packages/datav/index.vue";
import EchartDecoration from "./packages/decoration/index.vue";
import EchartBorderBox from "./packages/borderBox/index.vue";
import EchartClapper from "./packages/clappr/index.vue";
import EchartTime from "./packages/time/index.vue";
import EchartVue from "./packages/vue/index.vue";
export default {
	EchartVue,
	EchartRadar,
	EchartScatter,
	EchartFunnel,
	EchartTabs,
	EchartRectangle,
	EchartVideo,
	EchartWordCloud,
	EchartPictorialBar,
	EchartMaps,
	EchartImg,
	EchartImgBorder,
	EchartBar,
	EchartGauge,
	EchartIframe,
	EchartSwiper,
	EchartTable,
	EchartPie,
	EchartText,
	EchartLine,
	EchartFlop,
	EchartDatetime,
	EchartProgress,
	EchartCommon,
	EchartDatav,
	EchartDecoration,
	EchartBorderBox,
	EchartClapper,
	EchartTime,
};

<template>
	<el-card shadow="never" class="quick">
		<template #header>
			<div class="card-header">
				<span>
					<div class="person-bg">
						<el-icon size="14"><xitong2></xitong2></el-icon>
					</div>
					<label class="person-text">{{
						$t2("workplace.quick.operation")
					}}</label>
				</span>
			</div>
		</template>
		<el-row :gutter="8">
			<el-col
				v-for="(link, index) in links"
				:key="index"
				:span="8"
				class="wrapper"
			>
				<router-link :underline="false" :to="link.path">
					<el-icon
						size="18px"
						:style="{ color: link.color }"
						class="icon"
					>
						<component :is="link.icon"></component>
					</el-icon>
					<p class="text">
						{{ $t2(link.text) }}
					</p>
				</router-link>
			</el-col>
		</el-row>
	</el-card>
</template>

<script>
export default {
	data() {
		return {
			links: [
				{
					text: "t_user",
					icon: "yonghu7",
					color: "#5cadff",
					path: "/sys/user/UserList",
				},
				{
					text: "t_role",
					icon: "jiaose5",
					color: "#ff9900",
					path: "/sys/role/RoleList",
				},
				{
					text: "t_post",
					icon: "gangwei2",
					color: "#19be6b",
					path: "/sys/post/PostList",
				},
				{
					text: "t_office",
					icon: "jigou5",
					color: "#ed4014",
					path: "/sys/office/OfficeList",
				},
				{
					text: "t_menu",
					icon: "caidan3",
					color: "#fe84c0",
					path: "/sys/menu/MenuList",
				},
				{
					text: "t_area",
					icon: "quyu1",
					color: "#94de64",
					path: "/sys/area/AreaList",
				},
				{
					text: "t_tenant",
					icon: "zuhu3",
					color: "#ff9c6e",
					path: "/sys/tenant/TenantList",
				},
				{
					text: "t_language",
					icon: "duoyuyan2",
					color: "#b37fea",
					path: "/sys/language/LanguageList",
				},
				{
					text: "t_mailbox",
					icon: "xinxiang",
					color: "#5bdbd2",
					path: "/mailbox/index",
				},
				{
					text: "t_my_shedule",
					icon: "richeng2",
					color: "#ffc069",
					path: "/calendar/MyCalendar",
				},
				{
					text: "t_online",
					icon: "zaixianyonghu",
					color: "#795548",
					path: "/monitor/OnlineUserList",
				},
				{
					text: "t_log",
					icon: "rizhi2",
					color: "#1d31a3",
					path: "/sys/log/LogList",
				},
			],
		};
	},
};
</script>

<style lang="less">
.quick {
	border: none;
	.el-card__header {
		padding: calc(var(--el-card-padding) - 2px) var(--el-card-padding);
		border-bottom: 1px solid var(--el-card-border-color);
		box-sizing: border-box;
	}
	.person-bg {
		color: rgb(24, 144, 255);
		background-color: rgba(24, 144, 255, 0.15);
		line-height: 27px;
		width: 24px;
		height: 24px;
		border-radius: 50%;
		text-align: center;
		vertical-align: middle;
		display: inline-block;
	}
	.person-text {
		font-size: 15px;
		margin-left: 5px;
		font-weight: 400;
	}
}
.wrapper {
	margin-bottom: 18px;
	text-align: center;
	.el-link__inner {
		display: inline;
	}
	.icon {
		display: inline-block;

		margin-bottom: 4px;
		color: rgb(var(23, 23, 26));
		line-height: 32px;
		font-size: 16px;
		text-align: center;
		border-radius: 4px;
	}
	a:focus,
	a:hover {
		text-decoration: none;
	}
	.text {
		font-size: 12px;
		text-align: center;
		// color: rgb(78,89,105);
		display: block;
		margin-top: 0;
		margin-bottom: 1em;
	}
}
</style>

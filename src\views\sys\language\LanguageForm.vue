<template>
	<v-dialog :title="title" :close-on-click-modal="false" v-model="visible">
		<el-form
			:model="inputForm"
			ref="inputForm"
			v-loading="loading"
			:class="method === 'view' ? 'readonly' : ''"
			:disabled="method === 'view'"
			label-width="120px"
		>
			<el-row :gutter="15">
				<el-col :span="24">
					<el-form-item label="编码" prop="code" :rules="[]">
						<el-input
							v-model="inputForm.code"
							placeholder="请填写编码"
						></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="中文" prop="zh" :rules="[]">
						<el-input
							v-model="inputForm.zh"
							placeholder="请填写中文"
						></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="英文" prop="en" :rules="[]">
						<el-input
							v-model="inputForm.en"
							placeholder="请填写英文"
						></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="日本语" prop="ja" :rules="[]">
						<el-input
							v-model="inputForm.ja"
							placeholder="请填写日本语"
						></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">关闭</el-button>
				<el-button
					type="primary"
					v-if="method != 'view'"
					@click="doSubmit()"
					v-noMoreClick
					>确定</el-button
				>
			</span>
		</template>
	</v-dialog>
</template>
<script>
import languageService from "@/api/sys/languageService";
export default {
	data() {
		return {
			title: "",
			method: "",
			visible: false,
			loading: false,
			inputForm: {
				id: "",
				code: "",
				zh: "",
				en: "",
				ja: "",
			},
		};
	},
	components: {},
	methods: {
		init(method, id) {
			this.method = method;
			this.inputForm.id = id;
			if (method === "add") {
				this.title = `新建语言`;
			} else if (method === "edit") {
				this.title = "修改语言";
			} else if (method === "view") {
				this.title = "查看语言";
			}
			this.visible = true;
			this.loading = false;
			this.$nextTick(() => {
				this.$refs.inputForm.resetFields();
				if (method === "edit" || method === "view") {
					// 修改或者查看
					this.loading = true;
					languageService
						.queryById(this.inputForm.id)
						.then((data) => {
							this.inputForm = this.recover(this.inputForm, data);
							this.loading = false;
						});
				}
			});
		},
		// 表单提交
		doSubmit() {
			this.$refs["inputForm"].validate((valid) => {
				if (valid) {
					this.loading = true;
					languageService.save(this.inputForm).then((data) => {
						this.loading = false;
						this.visible = false;
						this.$message.success(data);
						this.$emit("refreshDataList");
					});
				}
			});
		},
	},
};
</script>

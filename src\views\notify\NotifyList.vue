<template>
	<div class="page">
		<el-form
			:inline="true"
			class="query-form m-b-10"
			v-if="searchVisible"
			ref="searchForm"
			:model="searchForm"
			@keyup.enter="refreshList()"
			@submit.prevent
		>
			<!-- 搜索框-->
			<el-form-item prop="title" label="标题：">
				<el-input
					v-model="searchForm.title"
					placeholder="请输入标题"
					clearable
				></el-input>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="refreshList()" icon="search"
					>查询</el-button
				>
				<el-button
					type="default"
					@click="resetSearch()"
					icon="refresh-right"
					>重置</el-button
				>
			</el-form-item>
		</el-form>
		<div class="jp-table">
			<vxe-toolbar
				ref="notifyToolbar"
				:refresh="{ query: refreshList }"
				export
				print
				custom
			>
				<template #buttons>
					<el-button
						v-if="hasPermission('notify:add')"
						type="primary"
						icon="plus"
						@click="add()"
						>新建</el-button
					>
					<el-button
						v-if="hasPermission('notify:del')"
						type="danger"
						icon="del-filled"
						@click="del()"
						v-show="
							$refs.notifyTable &&
							$refs.notifyTable.getCheckboxRecords().length > 0
						"
						>删除</el-button
					>
				</template>
				<template #tools>
					<vxe-button
						type="text"
						:title="searchVisible ? '收起检索' : '展开检索'"
						icon="vxe-icon-search"
						class="tool-btn"
						@click="searchVisible = !searchVisible"
					></vxe-button>
				</template>
			</vxe-toolbar>
			<div class="jp-table-body">
				<vxe-table
					border="inner"
					auto-resize
					resizable
					height="auto"
					:loading="loading"
					size="small"
					ref="notifyTable"
					show-header-overflow
					show-overflow
					highlight-hover-row
					:menu-config="{}"
					:print-config="{}"
					:import-config="{}"
					:export-config="{}"
					@sort-change="sortChangeHandle"
					:sort-config="{ remote: true }"
					:data="dataList"
					:checkbox-config="{}"
				>
					<vxe-column type="seq" width="40"></vxe-column>
					<vxe-column type="checkbox" width="40px"></vxe-column>
					<vxe-column title="标题" field="title" sortable>
						<template #default="scope">
							<el-link
								type="primary"
								:underline="false"
								v-if="
									hasPermission('notify:edit') &&
									scope.row.status === '0'
								"
								@click="edit(scope.row.id)"
								>{{ scope.row.title }}</el-link
							>
							<el-link
								type="primary"
								:underline="false"
								v-else-if="hasPermission('notify:view')"
								@click="view(scope.row.id)"
								>{{ scope.row.title }}</el-link
							>
							<span v-else>{{ scope.row.title }}</span>
						</template>
					</vxe-column>
					<vxe-column title="类型" field="type" sortable>
						<template #default="scope">
							{{
								$dictUtils.getDictLabel(
									"oa_notify_type",
									scope.row.type,
									"-"
								)
							}}
						</template>
					</vxe-column>
					<vxe-column
						title="内容"
						field="content"
						sortable
					></vxe-column>
					<vxe-column title="附件" field="files" sortable>
						<template #default="scope">
							<el-link
								v-show="scope.row.files"
								:href="item"
								target="_blank"
								style="margin-right: 8px"
								:key="index"
								v-for="(item, index) in (
									scope.row.files || ''
								).split(',')"
							>
								<el-tag>
									{{
										decodeURIComponent(
											item.substring(
												item.lastIndexOf("&name=") + 6
											)
										)
									}}</el-tag
								>
							</el-link>
						</template>
					</vxe-column>
					<vxe-column title="状态" field="status" sortable>
						<template #default="scope">
							<el-tag
								type="success"
								v-if="scope.row.status === '1'"
							>
								{{
									$dictUtils.getDictLabel(
										"oa_notify_status",
										scope.row.status,
										"-"
									)
								}}</el-tag
							>
							<el-tag
								type="danger"
								v-if="scope.row.status === '0'"
							>
								{{
									$dictUtils.getDictLabel(
										"oa_notify_status",
										scope.row.status,
										"-"
									)
								}}</el-tag
							>
						</template>
					</vxe-column>
					<vxe-column title="查阅状态" field="status" sortable>
						<template #default="scope">
							{{
								scope.row.readNum +
								"/" +
								(parseInt(scope.row.readNum) +
									parseInt(scope.row.unReadNum))
							}}
						</template>
					</vxe-column>
					<vxe-column
						title="发布者"
						field="createBy.name"
						sortable
					></vxe-column>

					<vxe-column
						title="操作"
						width="200px"
						fixed="right"
						align="center"
					>
						<template #default="scope">
							<el-button
								v-if="hasPermission('notify:view')"
								type="primary"
								text
								icon="view-filled"
								@click="view(scope.row.id)"
								>查看</el-button
							>
							<el-dropdown style="margin-left: 10px">
								<el-button text type="primary" icon="ArrowDown">
									更多
								</el-button>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item
											v-if="
												hasPermission('notify:edit') &&
												scope.row.status === '0'
											"
											><el-button
												text
												icon="edit-filled"
												type="primary"
												@click="edit(scope.row.id)"
												>修改</el-button
											></el-dropdown-item
										>
										<el-dropdown-item
											v-if="hasPermission('notify:del')"
										>
											<el-button
												text
												type="danger"
												icon="del-filled"
												@click="del(scope.row.id)"
												>删除</el-button
											></el-dropdown-item
										>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
						</template>
					</vxe-column>
				</vxe-table>
				<vxe-pager
					background
					size="small"
					:current-page="tablePage.currentPage"
					:page-size="tablePage.pageSize"
					:total="tablePage.total"
					:page-sizes="[
						10,
						20,
						100,
						1000,
						{ label: '全量数据', value: 1000000 },
					]"
					:layouts="[
						'PrevPage',
						'JumpNumber',
						'NextPage',
						'FullJump',
						'Sizes',
						'Total',
					]"
					@page-change="currentChangeHandle"
				>
				</vxe-pager>
			</div>
			<!-- 弹窗, 新增 / 修改 -->
			<NotifyForm
				ref="notifyForm"
				@refreshDataList="refreshList"
			></NotifyForm>
		</div>
	</div>
</template>

<script>
import NotifyForm from "./NotifyForm";
import notifyService from "@/api/notify/notifyService";
export default {
	data() {
		return {
			searchVisible: true,
			searchForm: {
				title: "",
			},
			dataList: [],
			tablePage: {
				total: 0,
				currentPage: 1,
				pageSize: 10,
				orders: [{ column: "a.create_time", asc: false }],
			},
			loading: false,
		};
	},
	components: {
		NotifyForm,
	},
	mounted() {
		this.$nextTick(() => {
			// 将表格和工具栏进行关联
			const $table = this.$refs.notifyTable;
			const $toolbar = this.$refs.notifyToolbar;
			$table.connect($toolbar);
		});
		this.refreshList();
	},

	methods: {
		// 获取数据列表
		refreshList() {
			this.loading = true;
			notifyService
				.list({
					current: this.tablePage.currentPage,
					size: this.tablePage.pageSize,
					orders: this.tablePage.orders,
					...this.searchForm,
				})
				.then((data) => {
					this.dataList = data.records;
					this.tablePage.total = data.total;
					this.loading = false;
				});
		},
		// 当前页
		currentChangeHandle({ currentPage, pageSize }) {
			this.tablePage.currentPage = currentPage;
			this.tablePage.pageSize = pageSize;
			this.refreshList();
		},
		// 排序
		sortChangeHandle(column) {
			this.tablePage.orders = [];
			if (column.order != null) {
				this.tablePage.orders.push({
					column: this.$utils.toLine(column.property),
					asc: column.order === "asc",
				});
			} else {
				this.tablePage.orders = [
					{ column: "a.create_time", asc: false },
				];
			}
			this.refreshList();
		},
		// 新增
		add() {
			this.$refs.notifyForm.init("add", "");
		},
		// 修改
		edit(id) {
			id =
				id ||
				this.$refs.notifyTable.getCheckboxRecords().map((item) => {
					return item.id;
				})[0];
			this.$refs.notifyForm.init("edit", id);
		},
		// 查看
		view(id) {
			this.$refs.notifyForm.init("view", id);
		},
		// 删除
		del(id) {
			let ids =
				id ||
				this.$refs.notifyTable
					.getCheckboxRecords()
					.map((item) => {
						return item.id;
					})
					.join(",");
			this.$confirm(`确定删除所选项吗?`, "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
			}).then(() => {
				this.loading = true;
				notifyService.delete(ids).then((data) => {
					this.$message.success(data);
					this.refreshList();
					this.loading = false;
				});
			});
		},
		resetSearch() {
			this.$refs.searchForm.resetFields();
			this.refreshList();
		},
	},
};
</script>

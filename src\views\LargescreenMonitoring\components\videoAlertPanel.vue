<template>
	<div class="video-alert-panel">

		<div class="main-header">
			<img src="@/assets/SohoPictures/Slice <EMAIL>" alt="告警列表" class="header-bg" />
			<div class="main-title">告警列表</div>
		</div>

		<div class="video-grid">
			<div
				class="grid-item"
				v-for="(video, index) in allAlertVideos"
				:key="index"
			>
				<div class="video-frame">
					<img
						v-if="video.pic"
						:src="video.pic"
						alt="告警图片"
						class="alert-image"
						@error="handleImageError"
						@click="openImagePreview(video.pic, video)"
					/>
					<div v-if="video.pic" class="zoom-icon" @click="openImagePreview(video.pic, video)">
						<i class="el-icon-zoom-in"></i>
					</div>
					<div v-else class="video-placeholder">
						<div class="placeholder-text">暂无图片</div>
					</div>
				</div>
				<div class="video-info" >
					<div class="location-info" @click="handleMonitorWarning(video)">
						<img src="@/assets/SohoPictures/Slice 13.png" alt="位置" class="info-icon" />
						<span>{{ video.areaName  }}</span>
					</div>
					<div class="time-info">
						<img src="@/assets/SohoPictures/Slice 14.png" alt="时间" class="info-icon" />
						<span>{{ video.alarmTime}}</span>
					</div>
					<!-- 告警类型信息 -->
					<div class="alert-info">
						<img src="@/assets/SohoPictures/Slice 15.png" alt="告警" class="info-icon" />
						<span>{{ video.type }}</span>
					</div>
				</div>
			</div>
		</div>
		<div class="pagination-container" v-if="total > 6">
			<el-pagination
				v-model:currentPage="currentPage"
				:page-size="pageSize"
				:total="total"
				:pager-count="5"
				layout="prev, pager, next"
				@current-change="handlePageChange"
				class="custom-pagination"
			/>
		</div>
		<el-dialog
			v-model="showImagePreview"
			title="告警图片详情"
			width="65%"

			:modal="true"
			:close-on-click-modal="true"
			:close-on-press-escape="true"
			:append-to-body="true"
			:destroy-on-close="true"
			center
			class="image-preview-dialog"
			style="z-index: 9999;"
		>
			<div class="preview-container">
				<div class="preview-main-area">
					<!-- 左侧切换按钮 -->
					<div
						class="switch-button switch-left"
						:class="{ disabled: !canSwitchPrevious() }"
						@click="switchToPrevious"
						v-if="allAlarmData.length > 1"
					>
						<i class="el-icon-arrow-left"></i>
					</div>

					<div class="preview-image-area">
						<img
							:src="previewImageUrl"
							alt="告警图片预览"
							class="preview-image"
							@error="handlePreviewImageError"
						/>
					</div>

					<!-- 右侧切换按钮 -->
					<div
						class="switch-button switch-right"
						:class="{ disabled: !canSwitchNext() }"
						@click="switchToNext"
						v-if="allAlarmData.length > 1"
					>
						<i class="el-icon-arrow-right"></i>
					</div>
				</div>
				<div class="preview-info-area" v-if="previewVideoInfo">
					<div class="info-row">
						<div class="info-item">
							<span class="info-label">告警位置：</span>
							<span class="info-value">{{ previewVideoInfo.areaName }}</span>
						</div>
						<div class="info-item">
							<span class="info-label">告警时间：</span>
							<span class="info-value">{{ previewVideoInfo.alarmTime }}</span>
						</div>
						<div class="info-item">
							<span class="info-label">告警类型：</span>
							<span class="info-value alert-type">{{ previewVideoInfo.type }}</span>
						</div>
					</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { gjList } from "@/api/suhao/index.js";
const alarmListData = ref([]);
const allAlertVideos = ref([]);
const currentPage = ref(1);
const pageSize = 6;
const total = ref(0);
const showImagePreview = ref(false);
const previewImageUrl = ref('');
const previewVideoInfo = ref(null);
// 新增：用于存储所有告警数据和当前预览索引
const allAlarmData = ref([]); // 存储所有告警数据
const currentPreviewIndex = ref(0); // 当前预览的数据索引
// 分页
const handlePageChange = (page) => {
	currentPage.value = page;
	fetchAlarmList();
};
const fetchAlarmList = async () => {
	try {
		const response = await gjList({
			current: currentPage.value,
			size: pageSize
		});
		alarmListData.value = response.records;
		total.value = response.total;
		if (alarmListData.value.length > 0) {
			processAlarmData();
		}
		// 获取所有告警数据用于切换功能
		await fetchAllAlarmData();
	} catch (error) {
		console.error("告警列表接口调用失败:", error);
	}
};

// 新增：获取所有告警数据的函数
const fetchAllAlarmData = async () => {
	try {
		const response = await gjList({
			current: 1,
			size: 9999 // 获取所有数据
		});
		allAlarmData.value = response.records || [];
	} catch (error) {
		console.error("获取所有告警数据失败:", error);
	}
};
const processAlarmData = () => {
	try {
		const processedAlertVideos = [];
		for (let i = 0; i < alarmListData.value.length; i++) {
			const alarmItem = alarmListData.value[i];

			const alarmVideoItem = {
				areaName: alarmItem.areaName, // 位置信息
				alarmTime: alarmItem.alarmtime, // 告警时间


				pic: alarmItem.pic, // 告警图片
				type: alarmItem.type, // 告警类型
				ip: alarmItem.ip // IP地址
			};
			processedAlertVideos.push(alarmVideoItem);
		}
		allAlertVideos.value = processedAlertVideos;

		// console.log('处理后的告警数据:', allAlertVideos.value);
	} catch (error) {
		console.error("告警数据处理失败:", error);
	}
};
// 图片加载失败处理
const handleImageError = (event) => {
	event.target.style.display = 'none';
};

const openImagePreview = (imageUrl, videoInfo) => {
	if (imageUrl) {
		previewImageUrl.value = imageUrl;
		previewVideoInfo.value = videoInfo;
		showImagePreview.value = true;
		// 找到当前预览数据在所有数据中的索引
		const index = allAlarmData.value.findIndex(item =>
			item.pic === videoInfo.pic &&
			item.areaName === videoInfo.areaName &&
			item.alarmtime === videoInfo.alarmTime
		);
		currentPreviewIndex.value = index !== -1 ? index : 0;
	}
};

// 预览图片加载失败处理
const handlePreviewImageError = (event) => {
	console.error('预览图片加载失败:', previewImageUrl.value);
	event.target.style.display = 'none';
};

// 新增：切换到上一条数据
const switchToPrevious = () => {
	if (currentPreviewIndex.value > 0) {
		currentPreviewIndex.value--;
		updatePreviewData();
	}
};

// 新增：切换到下一条数据
const switchToNext = () => {
	if (currentPreviewIndex.value < allAlarmData.value.length - 1) {
		currentPreviewIndex.value++;
		updatePreviewData();
	}
};

// 新增：更新预览数据
const updatePreviewData = () => {
	const currentData = allAlarmData.value[currentPreviewIndex.value];
	if (currentData) {
		previewImageUrl.value = currentData.pic;
		previewVideoInfo.value = {
			areaName: currentData.areaName,
			alarmTime: currentData.alarmtime,
			type: currentData.type,
			ip: currentData.ip,
			pic: currentData.pic
		};
	}
};

// 新增：判断是否可以切换上一条
const canSwitchPrevious = () => {
	return currentPreviewIndex.value > 0;
};

// 新增：判断是否可以切换下一条
const canSwitchNext = () => {
	return currentPreviewIndex.value < allAlarmData.value.length - 1;
};


const handleMonitorWarning = (video) => {
	console.log('触发监控告警操作，video对象:', video);
	const ip = video.ip;
	console.log('告警设备IP:', ip);
	// Unity环境
	if (window.vuplex && window.vuplex.postMessage) {
		window.vuplex.postMessage({
			funcname: 'U3DMonitorWarn',
			properties: {
				ip: ip
			}
		})
		console.log('已发送U3DMonitorWarn消息到Unity:', { ip: ip });
	} else {
		console.log('当前不在Unity环境中，模拟监控告警操作:', {
			funcname: 'U3DMonitorWarn',
			ip: ip
		});
	}
};

onMounted(() => {
	fetchAlarmList();
});
</script>

<style scoped lang="scss">
.video-alert-panel {
	position: absolute;
	bottom: 70px;
	width:78%;
	height: 35%;
	padding: 20px;
	z-index: 10;
	display: flex;
	flex-direction: column;
}
.main-header {
	position: relative;
	height: 35px;
	width: 100%;
	display: flex;
	align-items: center;
	margin-bottom: 15px;
	.header-bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 30%;
		height: 100%;
	}

	.main-title {
		position: relative;
		z-index: 2;

			color: #ffffff;
		display: flex;

		font-style:italic;
		transform: skew(-5deg);
		letter-spacing: 4px;
		margin-left: 43px;
		text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        text-align: left;
        font-style: normal;
        text-transform: none;
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-size: 20px;
	}
}

.video-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 10px;
	height: 100%;
}


.grid-item {
	display: flex;
	flex-direction: row;
	align-items: stretch;
	min-width:150px;
	max-height: 150px;
	margin-bottom: 4px;
}


.video-frame {
	position: relative;
	width: 45%;
	height: 100%;
	background: #0a1a2a;
	border-radius: 4px;
	overflow: hidden;
	flex-shrink: 0;
	cursor: pointer;
// background: red;
	&:hover {
		transform: scale(1.02);
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);

		.zoom-icon {
			opacity: 1;
		}
	}

	.alert-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
		border-radius: 4px;
		background: #000;
	}
}
.video-info {
	width: 65%;
	display: flex;
	flex-direction: column;
	color: #b8d4f0;
	font-size: 20px;
	padding-left: 8px;
	padding-right: 4px;
	margin-left: 8px;
	transition: all 0.3s ease;
	border-radius: 4px;
	.location-info, .time-info {
		display: flex;
		align-items: center;
		margin-bottom: 2px;
		max-width: 100%;
		.info-icon {
			width: 12px;
			height: 12px;
			margin-right: 4px;
			object-fit: contain;
			flex-shrink: 0;
			margin-top: 20px;
		}
		span {
			background: rgba(255,255,255,0.16);
			border-radius: 3px;
			padding: 2px 4px;
			font-size: 14px;
			margin-top: 20px;
		}
	}
	.alert-info {
		display: flex;
		align-items: center;
		margin-bottom: 2px;
		width: fit-content;
		max-width: 100%;
		margin-top: 20px;
		.info-icon {
			width: 12px;
			height: 12px;
			margin-right: 4px;
			flex-shrink: 0;
		}
		span {
			background: linear-gradient(180deg, rgba(108,49,49,0) 0%, rgba(255,29,29,0.55) 100%);
			// border-radius: 4px;
			padding: 2px 4px;
			color: #ffffff;
			font-size: 14px;
		}
	}
	.location-info {
		color: #ffffff;
		font-size: 10px;
	}

	.time-info {
		color: #fff;
		font-size: 9px;
	}
}
.pagination-container {
	position: absolute;
	bottom: -50px;
	left: 50%;
	transform: translateX(-50%);
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	z-index: 20;
	padding: 10px 0;
}
.custom-pagination {
	background: rgba(71, 108, 255, 0.1);
	border-radius: 8px;
	padding: 8px 16px;
	border: 1px solid rgba(71, 108, 255, 0.3);
	:deep(.el-pager li),
	:deep(.btn-prev),
	:deep(.btn-next) {
		background: #476CFF;
		color: #ffffff;
		border: none;
		border-radius: 6px;
		margin: 0 4px;
		min-width: 32px;
		height: 32px;
		font-weight: bold;
		transition: all 0.3s ease;
		&:hover {
			background: #5a7cff;
			transform: translateY(-1px);
		}
		&.is-active {
			background: #00ffff;
			color: #000000;
		}

		&:disabled {
			background: #666;
			color: #999;
			opacity: 0.5;
		}
	}

	:deep(.el-pager li) {
		font-size: 14px;
		line-height: 32px;
	}
	:deep(.btn-prev),
	:deep(.btn-next) {
		font-size: 16px;
	}
}
:deep(.image-preview-dialog) {
	z-index: 9999;
	.el-overlay {
		z-index: 9998;
		background-color: rgba(0, 0, 0, 0.8);
	}

	.el-dialog {
		background: rgba(15, 25, 45, 0.98);
		border: 2px solid #476CFF;
		border-radius: 12px;
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.8);
		margin: 5vh auto;
		max-height: 90vh;
		height: 90vh;
		overflow: hidden;
	}

	.el-dialog__header {
		background: linear-gradient(90deg, #476CFF 0%, #273777 100%);
		padding: 15px 25px;
		border-radius: 10px 10px 0 0;
		border-bottom: 1px solid rgba(71, 108, 255, 0.3);

		.el-dialog__title {
			color: #ffffff;
			font-size: 20px;
			font-weight: 600;
			text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
		}

		.el-dialog__close {
			color: #ffffff;
			font-size: 22px;
			transition: all 0.3s ease;

			&:hover {
				color: #00ffff;
				transform: scale(1.1);
			}
		}
	}

	.el-dialog__body {
		padding: 0;
		background: rgba(15, 25, 45, 0.95);
		overflow: hidden;
		height: calc(90vh - 140px);
		max-height: calc(90vh - 140px);
	}
}

.preview-container {
	display: flex;
	flex-direction: column;
	height: 100%;
	overflow: hidden;
	max-height: 100%;
}

.preview-main-area {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 15px;
	overflow: hidden;
	min-height: 0;
	max-height: calc(100% - 80px);
	position: relative;
}

.preview-image-area {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	background: #000;
	border-radius: 8px;
	overflow: hidden;

	.preview-image {
		max-width: 100%;
		max-height: 100%;
		width: auto;
		height: auto;
		object-fit: contain;
		border-radius: 8px;
		cursor: zoom-in;
		transition: transform 0.3s ease;
		display: block;

		&:hover {
			transform: scale(1.01);
		}
	}
}

// 新增：切换按钮样式
.switch-button {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	width: 50px;
	height: 50px;
	background: rgba(71, 108, 255, 0.8);
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	transition: all 0.3s ease;
	z-index: 10;
	color: #ffffff;
	font-size: 20px;
	border: 2px solid rgba(255, 255, 255, 0.3);

	&:hover {
		background: rgba(71, 108, 255, 1);
		transform: translateY(-50%) scale(1.1);
		box-shadow: 0 4px 12px rgba(71, 108, 255, 0.5);
	}

	&.switch-left {
		left: 20px;
	}

	&.switch-right {
		right: 20px;
	}

	&.disabled {
		background: rgba(100, 100, 100, 0.5);
		cursor: not-allowed;
		opacity: 0.5;

		&:hover {
			background: rgba(100, 100, 100, 0.5);
			transform: translateY(-50%);
			box-shadow: none;
		}
	}

	i {
		font-size: 18px;
		font-weight: bold;
	}
}

.preview-info-area {
	flex-shrink: 0;
	background: rgba(71, 108, 255, 0.15);
	border-top: 1px solid rgba(71, 108, 255, 0.3);
	padding: 15px 25px;

	.info-row {
		display: flex;
		justify-content: space-around;
		align-items: center;
		gap: 20px;
	}

	.info-item {
		display: flex;
		align-items: center;
		flex: 1;

		.info-label {
			// color: #B5C7E0;
			font-size: 14px;
			margin-right: 8px;
			white-space: nowrap;
		}

		.info-value {
			// color: #ffffff;
			font-size: 14px;
			background: rgba(255, 255, 255, 0.15);
			padding: 6px 12px;
			border-radius: 6px;
			flex: 1;
			text-align: center;
			transition: all 0.3s ease;

			&:hover {
				background: rgba(255, 255, 255, 0.25);
			}

			&.alert-type {
				background: linear-gradient(135deg, rgba(255,29,29,0.4) 0%, rgba(255,29,29,0.7) 100%);
				font-weight: 600;
				color: #ffffff;
				text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);

				&:hover {
					background: linear-gradient(135deg, rgba(255,29,29,0.5) 0%, rgba(255,29,29,0.8) 100%);
				}
			}
		}
	}
}

</style>
